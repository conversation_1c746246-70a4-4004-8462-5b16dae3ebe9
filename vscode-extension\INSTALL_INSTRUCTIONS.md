# 🚀 تعليمات تثبيت Agent Zero VS Code Extension

## 📦 الملف الجاهز للتثبيت

تم إنشاء الإصدار الجديد بنجاح:
**`agent-zero-vscode-0.1.0.vsix`**

## 🔧 كيفية التثبيت

### الطريقة 1: من خلال VS Code (الأسهل)

1. **افتح VS Code**
2. **اضغط Ctrl+Shift+P** لفتح Command Palette
3. **اكتب**: `Extensions: Install from VSIX...`
4. **اختر الملف**: `agent-zero-vscode-0.1.0.vsix`
5. **انقر Install**

### الطريقة 2: من خلال سطر الأوامر

```bash
code --install-extension agent-zero-vscode-0.1.0.vsix
```

## ✅ التحقق من التثبيت

بعد التثبيت:

1. **ابحث عن أيقونة Agent Zero** في Activity Bar (الشريط الجانبي الأيسر)
2. **انقر على الأيقونة** لفتح الواجهة
3. **يجب أن تظهر الواجهة الأصلية** لـ Agent Zero

## 📁 متطلبات مهمة

**تأكد من الهيكل التالي:**

```
agent-zero/
├── webui/                  # ⚠️ مطلوب!
│   ├── index.html
│   ├── index.js
│   ├── index.css
│   ├── css/
│   ├── js/
│   ├── public/
│   └── vendor/
└── vscode-extension/       # مجلد الإضافة
    └── agent-zero-vscode-0.1.0.vsix
```

**⚠️ مهم جداً**: يجب أن يكون مجلد `webui` في نفس مستوى مجلد `vscode-extension`

## 🎯 كيفية الاستخدام

### فتح الواجهة
- **انقر على أيقونة Agent Zero** في Activity Bar
- أو **Ctrl+Shift+P** ثم `Agent Zero: Open Chat`

### ما ستراه
- **الواجهة الأصلية** لـ Agent Zero كما هي
- **جميع الوظائف** تعمل بشكل طبيعي
- **التكامل مع VS Code** (ثيمات، إلخ)

## 🔍 استكشاف الأخطاء

### المشكلة: "WebUI Loading Error"
**السبب**: مجلد webui غير موجود في المكان الصحيح

**الحل**:
1. تأكد من وجود مجلد `webui` في نفس مستوى `vscode-extension`
2. تأكد من وجود ملف `webui/index.html`
3. أعد تشغيل VS Code

### المشكلة: الإضافة لا تظهر
**الحل**:
1. تأكد من نجاح التثبيت
2. أعد تشغيل VS Code
3. ابحث عن "Agent Zero" في قائمة Extensions

### المشكلة: الواجهة لا تحمل بشكل صحيح
**الحل**:
1. تحقق من وجود جميع ملفات CSS و JS في مجلد webui
2. افتح Developer Tools (F12) وتحقق من الأخطاء
3. تأكد من صحة مسارات الملفات

## 📋 معلومات الإصدار

- **الإصدار**: 0.1.0
- **الحجم**: 262.86 KB
- **الملفات المضمنة**: 42 ملف
- **التوافق**: VS Code 1.74.0+

## 🎉 الميزات

- ✅ **عرض الواجهة الأصلية** بدون تعديل
- ✅ **تحميل جميع الملفات** (CSS, JS, صور)
- ✅ **تكامل VS Code** مع دعم الثيمات
- ✅ **سهولة الاستخدام** - فقط انقر وابدأ

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تأكد من اتباع التعليمات بدقة
2. تحقق من هيكل المجلدات
3. أعد تشغيل VS Code
4. تحقق من وحدة التحكم للأخطاء

---

**استمتع باستخدام Agent Zero في VS Code!** 🎯

## 🔄 إلغاء التثبيت

إذا أردت إلغاء التثبيت:
1. اذهب إلى Extensions في VS Code
2. ابحث عن "Agent Zero"
3. انقر على "Uninstall"
