# Agent Zero VS Code Extension - Original WebUI

هذه الإضافة تعرض الواجهة الأصلية لـ Agent Zero داخل VS Code بدون أي تعديل.

## 🚀 كيفية الاستخدام

### 1. التثبيت
- تأكد من أن مجلد `webui` موجود في نفس مستوى مجلد `vscode-extension`
- افتح VS Code
- اذهب إلى Extensions (Ctrl+Shift+X)
- ابحث عن "Agent Zero" أو قم بتثبيت الإضافة يدوياً

### 2. فتح الواجهة
- انقر على أيقونة Agent Zero في Activity Bar (الشريط الجانبي)
- أو استخدم Ctrl+Shift+A
- ستظهر الواجهة الأصلية لـ Agent Zero

### 3. الهيكل المطلوب
```
agent-zero/
├── webui/                  # الواجهة الأصلية
│   ├── index.html
│   ├── index.js
│   ├── index.css
│   ├── css/
│   ├── js/
│   ├── public/
│   └── vendor/
└── vscode-extension/       # إضافة VS Code
    ├── src/
    ├── media/
    └── package.json
```

## ✅ الميزات

- **الواجهة الأصلية**: عرض الـ webui الأصلي كما هو بدون تعديل
- **تكامل VS Code**: يعمل داخل VS Code مع الحفاظ على جميع الوظائف
- **سهولة الاستخدام**: فقط انقر على الأيقونة وابدأ الاستخدام

## 🔧 استكشاف الأخطاء

### المشكلة: "WebUI Loading Error"
**الحل**: تأكد من أن مجلد `webui` موجود في المكان الصحيح:
- يجب أن يكون `webui` في نفس مستوى `vscode-extension`
- تأكد من وجود ملف `webui/index.html`

### المشكلة: الملفات لا تحمل بشكل صحيح
**الحل**: 
- تأكد من أن جميع ملفات CSS و JS موجودة في مجلداتها
- أعد تشغيل VS Code
- تحقق من وحدة التحكم للأخطاء

## 📝 ملاحظات

- هذه الإضافة تعرض الواجهة الأصلية فقط
- لا تحتاج إلى أي إعداد إضافي
- تعمل مع جميع ثيمات VS Code

## 🎯 الهدف

الهدف من هذه الإضافة هو توفير طريقة سهلة لاستخدام Agent Zero داخل VS Code مع الحفاظ على الواجهة الأصلية كما هي.

---

**استمتع باستخدام Agent Zero في VS Code!** 🎉
