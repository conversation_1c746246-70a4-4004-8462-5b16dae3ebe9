// Agent Zero VS Code Extension - Main JavaScript
(function() {
    'use strict';
    
    // Get VS Code API
    const vscode = acquireVsCodeApi();
    
    // State management
    let messages = [];
    let chats = [];
    let tasks = [];
    let isTyping = false;
    let autoScroll = true;
    let currentContext = '';
    let connectionStatus = true;
    let attachments = [];
    let paused = false;
    
    // DOM elements
    let messageInput;
    let messagesContainer;
    let sendBtn;
    let leftPanel;
    let rightPanel;
    let chatsSection;
    let tasksSection;
    let chatsList;
    let tasksList;
    let previewSection;
    let timeDate;
    
    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        initializeElements();
        setupEventListeners();
        updateTime();
        setupTabs();
        setupSidebarToggle();
        
        // Update time every minute
        setInterval(updateTime, 60000);
        
        // Initialize with welcome message
        showWelcomeMessage();
    });
    
    function initializeElements() {
        messageInput = document.getElementById('chat-input');
        messagesContainer = document.getElementById('chat-history');
        sendBtn = document.getElementById('send-button');
        leftPanel = document.getElementById('left-panel');
        rightPanel = document.getElementById('right-panel');
        chatsSection = document.getElementById('chats-section');
        tasksSection = document.getElementById('tasks-section');
        chatsList = document.getElementById('chats-list');
        tasksList = document.getElementById('tasks-list');
        previewSection = document.getElementById('preview-section');
        timeDate = document.getElementById('time-date');
    }
    
    function setupEventListeners() {
        // Send button click
        if (sendBtn) {
            sendBtn.addEventListener('click', sendMessage);
        }
        
        // Enter key to send (Shift+Enter for new line)
        if (messageInput) {
            messageInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
            
            // Auto-resize textarea
            messageInput.addEventListener('input', function() {
                autoResizeTextarea();
                updateSendButton();
            });
        }
        
        // Control buttons
        setupControlButtons();
        
        // File input
        setupFileInput();
        
        // Text buttons
        setupTextButtons();
        
        // Preferences
        setupPreferences();
        
        // Handle messages from extension
        window.addEventListener('message', handleExtensionMessage);
    }
    
    function setupControlButtons() {
        const buttons = {
            'resetChat': () => vscode.postMessage({ type: 'resetChat' }),
            'newChat': () => vscode.postMessage({ type: 'newChat' }),
            'loadChats': () => vscode.postMessage({ type: 'loadChats' }),
            'saveChat': () => vscode.postMessage({ type: 'saveChat' }),
            'restart': () => vscode.postMessage({ type: 'restart' }),
            'settings': () => vscode.postMessage({ type: 'openSettings' })
        };
        
        Object.keys(buttons).forEach(id => {
            const btn = document.getElementById(id);
            if (btn) {
                btn.addEventListener('click', buttons[id]);
            }
        });
    }
    
    function setupFileInput() {
        const fileInput = document.getElementById('file-input');
        if (fileInput) {
            fileInput.addEventListener('change', handleFileUpload);
        }
    }
    
    function setupTextButtons() {
        const buttons = {
            'pause-agent': () => {
                paused = !paused;
                updatePauseButton();
                vscode.postMessage({ type: 'pauseAgent', paused: paused });
            },
            'load-knowledge': () => vscode.postMessage({ type: 'loadKnowledge' }),
            'work_dir_browser': () => vscode.postMessage({ type: 'openFileBrowser' }),
            'history_inspect': () => vscode.postMessage({ type: 'openHistory' })
        };
        
        Object.keys(buttons).forEach(id => {
            const btn = document.getElementById(id);
            if (btn) {
                btn.addEventListener('click', buttons[id]);
            }
        });
    }
    
    function setupPreferences() {
        const autoScrollSwitch = document.getElementById('auto-scroll-switch');
        const darkModeSwitch = document.getElementById('dark-mode-switch');
        const showThoughtsSwitch = document.getElementById('show-thoughts-switch');
        
        if (autoScrollSwitch) {
            autoScrollSwitch.addEventListener('change', (e) => {
                autoScroll = e.target.checked;
                vscode.postMessage({ 
                    type: 'updatePreference', 
                    key: 'autoScroll', 
                    value: autoScroll 
                });
            });
        }
        
        if (darkModeSwitch) {
            darkModeSwitch.addEventListener('change', (e) => {
                const isDark = e.target.checked;
                document.body.classList.toggle('dark-mode', isDark);
                document.body.classList.toggle('light-mode', !isDark);
                vscode.postMessage({ 
                    type: 'updatePreference', 
                    key: 'darkMode', 
                    value: isDark 
                });
            });
        }
        
        if (showThoughtsSwitch) {
            showThoughtsSwitch.addEventListener('change', (e) => {
                vscode.postMessage({ 
                    type: 'updatePreference', 
                    key: 'showThoughts', 
                    value: e.target.checked 
                });
            });
        }
    }
    
    function setupTabs() {
        const chatsTab = document.getElementById('chats-tab');
        const tasksTab = document.getElementById('tasks-tab');
        
        if (chatsTab) {
            chatsTab.addEventListener('click', () => {
                switchTab('chats');
            });
        }
        
        if (tasksTab) {
            tasksTab.addEventListener('click', () => {
                switchTab('tasks');
            });
        }
    }
    
    function setupSidebarToggle() {
        const toggleBtn = document.getElementById('toggle-sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        
        if (toggleBtn) {
            toggleBtn.addEventListener('click', toggleSidebar);
        }
        
        if (overlay) {
            overlay.addEventListener('click', () => {
                if (isMobile()) {
                    toggleSidebar(false);
                }
            });
        }
        
        // Handle resize
        window.addEventListener('resize', handleResize);
        handleResize(); // Initial call
    }
    
    function sendMessage() {
        const message = messageInput.value.trim();
        if (!message || isTyping) return;
        
        // Add attachments if any
        const messageData = {
            text: message,
            attachments: attachments.slice() // Copy array
        };
        
        // Send message to extension
        vscode.postMessage({
            type: 'sendMessage',
            message: messageData
        });
        
        // Clear input and attachments
        messageInput.value = '';
        attachments = [];
        updateAttachmentPreview();
        autoResizeTextarea();
        updateSendButton();
    }
    
    function addMessage(messageData) {
        messages.push(messageData);
        
        const messageElement = createMessageElement(messageData);
        
        // Remove welcome message if it exists
        const welcomeMessage = messagesContainer.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }
        
        messagesContainer.appendChild(messageElement);
        
        if (autoScroll) {
            scrollToBottom();
        }
    }
    
    function createMessageElement(messageData) {
        const messageDiv = document.createElement('div');
        const containerClass = messageData.sender === 'user' ? 'user-container' : 
                             messageData.sender === 'agent' ? 'ai-container' : 'center-container';
        
        messageDiv.className = containerClass;
        
        const messageContent = document.createElement('div');
        messageContent.className = `message message-${messageData.sender}`;
        
        // Message header
        const header = document.createElement('div');
        header.className = 'message-header';
        header.innerHTML = `
            <span class="message-sender">${getSenderName(messageData.sender)}</span>
            <span class="message-timestamp">${formatTimestamp(messageData.timestamp)}</span>
        `;
        
        // Message body
        const body = document.createElement('div');
        body.className = 'message-body';
        body.innerHTML = processMessageContent(messageData.content);
        
        messageContent.appendChild(header);
        messageContent.appendChild(body);
        messageDiv.appendChild(messageContent);
        
        return messageDiv;
    }
    
    function processMessageContent(content) {
        // Basic processing - escape HTML first
        let processed = content.replace(/&/g, '&amp;')
                              .replace(/</g, '&lt;')
                              .replace(/>/g, '&gt;');
        
        // Handle code blocks
        processed = processed.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
            return `<pre><code class="language-${lang || 'text'}">${code.trim()}</code></pre>`;
        });
        
        // Handle inline code
        processed = processed.replace(/`([^`]+)`/g, '<code>$1</code>');
        
        // Handle bold text
        processed = processed.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        
        // Handle italic text
        processed = processed.replace(/\*(.*?)\*/g, '<em>$1</em>');
        
        // Handle line breaks
        processed = processed.replace(/\n/g, '<br>');
        
        return processed;
    }
    
    function getSenderName(sender) {
        switch (sender) {
            case 'user': return 'You';
            case 'agent': return 'Agent Zero';
            case 'system': return 'System';
            default: return sender;
        }
    }
    
    function formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString();
    }
    
    // Utility functions
    function handleFileUpload(event) {
        const files = event.target.files;

        Array.from(files).forEach(file => {
            const ext = file.name.split('.').pop().toLowerCase();
            const isImage = ['jpg', 'jpeg', 'png', 'bmp', 'gif'].includes(ext);

            if (isImage) {
                const reader = new FileReader();
                reader.onload = e => {
                    attachments.push({
                        file: file,
                        url: e.target.result,
                        type: 'image',
                        name: file.name,
                        extension: ext
                    });
                    updateAttachmentPreview();
                };
                reader.readAsDataURL(file);
            } else {
                attachments.push({
                    file: file,
                    type: 'file',
                    name: file.name,
                    extension: ext
                });
                updateAttachmentPreview();
            }
        });

        // Clear file input
        event.target.value = '';
    }

    function updateAttachmentPreview() {
        if (!previewSection) return;

        if (attachments.length === 0) {
            previewSection.style.display = 'none';
            previewSection.innerHTML = '';
            return;
        }

        previewSection.style.display = 'flex';
        previewSection.innerHTML = '';

        attachments.forEach((attachment, index) => {
            const previewItem = document.createElement('div');
            previewItem.className = `preview-item ${attachment.type === 'image' ? 'image-preview' : ''}`;

            if (attachment.type === 'image') {
                previewItem.innerHTML = `
                    <img src="${attachment.url}" alt="${attachment.name}">
                    <button class="remove-attachment" onclick="removeAttachment(${index})">&times;</button>
                `;
            } else {
                previewItem.innerHTML = `
                    <div class="file-preview">
                        <span class="filename">${attachment.name}</span>
                        <span class="extension">${attachment.extension.toUpperCase()}</span>
                    </div>
                    <button class="remove-attachment" onclick="removeAttachment(${index})">&times;</button>
                `;
            }

            previewSection.appendChild(previewItem);
        });
    }

    function removeAttachment(index) {
        attachments.splice(index, 1);
        updateAttachmentPreview();
        updateSendButton();
    }

    function autoResizeTextarea() {
        if (!messageInput) return;
        messageInput.style.height = 'auto';
        messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
    }

    function updateSendButton() {
        if (!sendBtn || !messageInput) return;
        const hasMessage = messageInput.value.trim().length > 0;
        const hasAttachments = attachments.length > 0;
        sendBtn.disabled = (!hasMessage && !hasAttachments) || isTyping;
    }

    function updatePauseButton() {
        const pauseBtn = document.getElementById('pause-agent');
        if (!pauseBtn) return;

        const icon = pauseBtn.querySelector('svg');
        const text = pauseBtn.querySelector('span');

        if (paused) {
            icon.innerHTML = '<path d="M8 5v14l11-7z"></path>';
            text.textContent = 'Resume Agent';
        } else {
            icon.innerHTML = '<path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"></path>';
            text.textContent = 'Pause Agent';
        }
    }

    function switchTab(tabName) {
        const chatsTab = document.getElementById('chats-tab');
        const tasksTab = document.getElementById('tasks-tab');

        if (tabName === 'chats') {
            chatsTab.classList.add('active');
            tasksTab.classList.remove('active');
            chatsSection.style.display = 'block';
            tasksSection.style.display = 'none';
        } else {
            tasksTab.classList.add('active');
            chatsTab.classList.remove('active');
            tasksSection.style.display = 'block';
            chatsSection.style.display = 'none';
        }
    }

    function toggleSidebar(show) {
        if (typeof show === 'boolean') {
            leftPanel.classList.toggle('hidden', !show);
            rightPanel.classList.toggle('expanded', !show);
            document.getElementById('sidebar-overlay').classList.toggle('visible', show && isMobile());
        } else {
            leftPanel.classList.toggle('hidden');
            rightPanel.classList.toggle('expanded');
            const isHidden = leftPanel.classList.contains('hidden');
            document.getElementById('sidebar-overlay').classList.toggle('visible', !isHidden && isMobile());
        }
    }

    function isMobile() {
        return window.innerWidth <= 768;
    }

    function handleResize() {
        if (isMobile()) {
            leftPanel.classList.add('hidden');
            rightPanel.classList.add('expanded');
            document.getElementById('sidebar-overlay').classList.remove('visible');
        } else {
            leftPanel.classList.remove('hidden');
            rightPanel.classList.remove('expanded');
            document.getElementById('sidebar-overlay').classList.remove('visible');
        }
    }

    function updateTime() {
        if (!timeDate) return;
        const now = new Date();
        timeDate.textContent = now.toLocaleString();
    }

    function scrollToBottom() {
        if (!messagesContainer) return;
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    function showWelcomeMessage() {
        if (!messagesContainer) return;
        messagesContainer.innerHTML = `
            <div class="welcome-message">
                <h3>Welcome to Agent Zero!</h3>
                <p>I'm your AI assistant. How can I help you today?</p>
            </div>
        `;
    }

    function setTyping(typing) {
        isTyping = typing;
        updateSendButton();

        // Show/hide typing indicator if it exists
        const indicator = document.getElementById('typing-indicator');
        if (indicator) {
            indicator.style.display = typing ? 'flex' : 'none';
        }

        if (typing && autoScroll) {
            scrollToBottom();
        }
    }

    function clearMessages() {
        messages = [];
        showWelcomeMessage();
    }

    function updateChats(chatsData) {
        chats = chatsData;
        if (!chatsList) return;

        chatsList.innerHTML = '';

        if (chats.length === 0) {
            document.getElementById('empty-chats-message').style.display = 'block';
            return;
        }

        document.getElementById('empty-chats-message').style.display = 'none';

        chats.forEach(chat => {
            const li = document.createElement('li');
            li.innerHTML = `
                <div class="chat-list-button" onclick="selectChat('${chat.id}')">
                    <span class="chat-name" title="${chat.name || 'Chat #' + chat.no}">
                        ${chat.name || 'Chat #' + chat.no}
                    </span>
                </div>
                <button class="edit-button" onclick="deleteChat('${chat.id}')">X</button>
            `;
            chatsList.appendChild(li);
        });
    }

    function updateTasks(tasksData) {
        tasks = tasksData;
        if (!tasksList) return;

        tasksList.innerHTML = '';

        if (tasks.length === 0) {
            document.getElementById('empty-tasks-message').style.display = 'block';
            return;
        }

        document.getElementById('empty-tasks-message').style.display = 'none';

        tasks.forEach(task => {
            const li = document.createElement('li');
            li.innerHTML = `
                <div class="chat-list-button has-task-container" onclick="selectTask('${task.id}')">
                    <div class="task-container task-container-vertical">
                        <span class="task-name" title="${task.task_name || 'Task #' + task.no}">
                            ${task.task_name || 'Task #' + task.no}
                        </span>
                        <div class="task-info-line">
                            <span class="scheduler-status-badge scheduler-status-badge-small scheduler-status-${task.state || 'idle'}">
                                ${task.state || 'idle'}
                            </span>
                            <button class="edit-button" onclick="openTaskDetail('${task.id}')" title="View task details">
                                ℹ️
                            </button>
                            <button class="edit-button" onclick="deleteTask('${task.id}')" title="Delete task">X</button>
                        </div>
                    </div>
                </div>
            `;
            tasksList.appendChild(li);
        });
    }

    // Global functions for onclick handlers
    window.removeAttachment = removeAttachment;
    window.selectChat = (chatId) => vscode.postMessage({ type: 'selectChat', chatId });
    window.deleteChat = (chatId) => vscode.postMessage({ type: 'deleteChat', chatId });
    window.selectTask = (taskId) => vscode.postMessage({ type: 'selectTask', taskId });
    window.deleteTask = (taskId) => vscode.postMessage({ type: 'deleteTask', taskId });
    window.openTaskDetail = (taskId) => vscode.postMessage({ type: 'openTaskDetail', taskId });

    // Handle messages from extension
    function handleExtensionMessage(event) {
        const message = event.data;

        switch (message.type) {
            case 'addMessage':
                addMessage(message.message);
                break;
            case 'setTyping':
                setTyping(message.isTyping);
                break;
            case 'clearMessages':
                clearMessages();
                break;
            case 'loadMessages':
                clearMessages();
                message.messages.forEach(msg => addMessage(msg));
                break;
            case 'updateChats':
                updateChats(message.chats);
                break;
            case 'updateTasks':
                updateTasks(message.tasks);
                break;
            case 'updateConnectionStatus':
                connectionStatus = message.connected;
                updateConnectionIndicator();
                break;
            case 'showToast':
                showToast(message.toast);
                break;
        }
    }

    function updateConnectionIndicator() {
        const connectedCircle = document.querySelector('.connected-circle');
        const disconnectedCircle = document.querySelector('.disconnected-circle');

        if (connectedCircle) {
            connectedCircle.style.opacity = connectionStatus ? '1' : '0';
        }
        if (disconnectedCircle) {
            disconnectedCircle.style.opacity = connectionStatus ? '0' : '1';
        }
    }

    function showToast(toast) {
        // Simple toast implementation
        const toastEl = document.getElementById('toast');
        if (!toastEl) return;

        toastEl.querySelector('.toast__title').textContent = toast.title || '';
        toastEl.querySelector('.toast__message').textContent = toast.message || '';
        toastEl.className = `toast ${toast.type || 'info'}`;
        toastEl.style.display = 'block';

        // Auto-hide after 5 seconds
        setTimeout(() => {
            toastEl.style.display = 'none';
        }, 5000);
    }

})();
