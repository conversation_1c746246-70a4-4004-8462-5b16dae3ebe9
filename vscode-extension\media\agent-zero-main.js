// Agent Zero VS Code Extension - Main JavaScript
(function() {
    'use strict';
    
    // Get VS Code API
    const vscode = acquireVsCodeApi();
    
    // State management
    let messages = [];
    let chats = [];
    let tasks = [];
    let isTyping = false;
    let autoScroll = true;
    let currentContext = '';
    let connectionStatus = true;
    let attachments = [];
    let paused = false;
    
    // DOM elements
    let messageInput;
    let messagesContainer;
    let sendBtn;
    let leftPanel;
    let rightPanel;
    let chatsSection;
    let tasksSection;
    let chatsList;
    let tasksList;
    let previewSection;
    let timeDate;
    
    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        initializeElements();
        setupEventListeners();
        updateTime();
        setupTabs();
        setupSidebarToggle();
        
        // Update time every minute
        setInterval(updateTime, 60000);
        
        // Initialize with welcome message
        showWelcomeMessage();
    });
    
    function initializeElements() {
        messageInput = document.getElementById('chat-input');
        messagesContainer = document.getElementById('chat-history');
        sendBtn = document.getElementById('send-button');
        leftPanel = document.getElementById('left-panel');
        rightPanel = document.getElementById('right-panel');
        chatsSection = document.getElementById('chats-section');
        tasksSection = document.getElementById('tasks-section');
        chatsList = document.getElementById('chats-list');
        tasksList = document.getElementById('tasks-list');
        previewSection = document.getElementById('preview-section');
        timeDate = document.getElementById('time-date');
    }
    
    function setupEventListeners() {
        // Send button click
        if (sendBtn) {
            sendBtn.addEventListener('click', sendMessage);
        }
        
        // Enter key to send (Shift+Enter for new line)
        if (messageInput) {
            messageInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
            
            // Auto-resize textarea
            messageInput.addEventListener('input', function() {
                autoResizeTextarea();
                updateSendButton();
            });
        }
        
        // Control buttons
        setupControlButtons();
        
        // File input
        setupFileInput();
        
        // Text buttons
        setupTextButtons();
        
        // Preferences
        setupPreferences();
        
        // Handle messages from extension
        window.addEventListener('message', handleExtensionMessage);
    }
    
    function setupControlButtons() {
        const buttons = {
            'resetChat': () => vscode.postMessage({ type: 'resetChat' }),
            'newChat': () => vscode.postMessage({ type: 'newChat' }),
            'loadChats': () => vscode.postMessage({ type: 'loadChats' }),
            'saveChat': () => vscode.postMessage({ type: 'saveChat' }),
            'restart': () => vscode.postMessage({ type: 'restart' }),
            'settings': () => vscode.postMessage({ type: 'openSettings' })
        };
        
        Object.keys(buttons).forEach(id => {
            const btn = document.getElementById(id);
            if (btn) {
                btn.addEventListener('click', buttons[id]);
            }
        });
    }
    
    function setupFileInput() {
        const fileInput = document.getElementById('file-input');
        if (fileInput) {
            fileInput.addEventListener('change', handleFileUpload);
        }
    }
    
    function setupTextButtons() {
        const buttons = {
            'pause-agent': () => {
                paused = !paused;
                updatePauseButton();
                vscode.postMessage({ type: 'pauseAgent', paused: paused });
            },
            'load-knowledge': () => vscode.postMessage({ type: 'loadKnowledge' }),
            'work_dir_browser': () => vscode.postMessage({ type: 'openFileBrowser' }),
            'history_inspect': () => vscode.postMessage({ type: 'openHistory' }),
            'search_button': () => openSearchModal(),
            'memory_button': () => openMemoryModal()
        };

        Object.keys(buttons).forEach(id => {
            const btn = document.getElementById(id);
            if (btn) {
                btn.addEventListener('click', buttons[id]);
            }
        });
    }
    
    function setupPreferences() {
        const autoScrollSwitch = document.getElementById('auto-scroll-switch');
        const darkModeSwitch = document.getElementById('dark-mode-switch');
        const showThoughtsSwitch = document.getElementById('show-thoughts-switch');
        
        if (autoScrollSwitch) {
            autoScrollSwitch.addEventListener('change', (e) => {
                autoScroll = e.target.checked;
                vscode.postMessage({ 
                    type: 'updatePreference', 
                    key: 'autoScroll', 
                    value: autoScroll 
                });
            });
        }
        
        if (darkModeSwitch) {
            darkModeSwitch.addEventListener('change', (e) => {
                const isDark = e.target.checked;
                document.body.classList.toggle('dark-mode', isDark);
                document.body.classList.toggle('light-mode', !isDark);
                vscode.postMessage({ 
                    type: 'updatePreference', 
                    key: 'darkMode', 
                    value: isDark 
                });
            });
        }
        
        if (showThoughtsSwitch) {
            showThoughtsSwitch.addEventListener('change', (e) => {
                vscode.postMessage({ 
                    type: 'updatePreference', 
                    key: 'showThoughts', 
                    value: e.target.checked 
                });
            });
        }
    }
    
    function setupTabs() {
        const chatsTab = document.getElementById('chats-tab');
        const tasksTab = document.getElementById('tasks-tab');
        const createTaskBtn = document.getElementById('create-task-btn');

        if (chatsTab) {
            chatsTab.addEventListener('click', () => {
                switchTab('chats');
            });
        }

        if (tasksTab) {
            tasksTab.addEventListener('click', () => {
                switchTab('tasks');
            });
        }

        if (createTaskBtn) {
            createTaskBtn.addEventListener('click', () => {
                createNewTask();
            });
        }
    }
    
    function setupSidebarToggle() {
        const toggleBtn = document.getElementById('toggle-sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        
        if (toggleBtn) {
            toggleBtn.addEventListener('click', toggleSidebar);
        }
        
        if (overlay) {
            overlay.addEventListener('click', () => {
                if (isMobile()) {
                    toggleSidebar(false);
                }
            });
        }
        
        // Handle resize
        window.addEventListener('resize', handleResize);
        handleResize(); // Initial call
    }
    
    function sendMessage() {
        const message = messageInput.value.trim();
        if (!message || isTyping) return;
        
        // Add attachments if any
        const messageData = {
            text: message,
            attachments: attachments.slice() // Copy array
        };
        
        // Send message to extension
        vscode.postMessage({
            type: 'sendMessage',
            message: messageData
        });
        
        // Clear input and attachments
        messageInput.value = '';
        attachments = [];
        updateAttachmentPreview();
        autoResizeTextarea();
        updateSendButton();
    }
    
    function addMessage(messageData) {
        messages.push(messageData);
        
        const messageElement = createMessageElement(messageData);
        
        // Remove welcome message if it exists
        const welcomeMessage = messagesContainer.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }
        
        messagesContainer.appendChild(messageElement);
        
        if (autoScroll) {
            scrollToBottom();
        }
    }
    
    function createMessageElement(messageData) {
        const messageDiv = document.createElement('div');
        const containerClass = messageData.sender === 'user' ? 'user-container' : 
                             messageData.sender === 'agent' ? 'ai-container' : 'center-container';
        
        messageDiv.className = containerClass;
        
        const messageContent = document.createElement('div');
        messageContent.className = `message message-${messageData.sender}`;
        
        // Message header
        const header = document.createElement('div');
        header.className = 'message-header';
        header.innerHTML = `
            <span class="message-sender">${getSenderName(messageData.sender)}</span>
            <span class="message-timestamp">${formatTimestamp(messageData.timestamp)}</span>
        `;
        
        // Message body
        const body = document.createElement('div');
        body.className = 'message-body';
        body.innerHTML = processMessageContent(messageData.content);
        
        messageContent.appendChild(header);
        messageContent.appendChild(body);
        messageDiv.appendChild(messageContent);
        
        return messageDiv;
    }
    
    function processMessageContent(content) {
        // Basic processing - escape HTML first
        let processed = content.replace(/&/g, '&amp;')
                              .replace(/</g, '&lt;')
                              .replace(/>/g, '&gt;');
        
        // Handle code blocks
        processed = processed.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
            return `<pre><code class="language-${lang || 'text'}">${code.trim()}</code></pre>`;
        });
        
        // Handle inline code
        processed = processed.replace(/`([^`]+)`/g, '<code>$1</code>');
        
        // Handle bold text
        processed = processed.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        
        // Handle italic text
        processed = processed.replace(/\*(.*?)\*/g, '<em>$1</em>');
        
        // Handle line breaks
        processed = processed.replace(/\n/g, '<br>');
        
        return processed;
    }
    
    function getSenderName(sender) {
        switch (sender) {
            case 'user': return 'You';
            case 'agent': return 'Agent Zero';
            case 'system': return 'System';
            default: return sender;
        }
    }
    
    function formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString();
    }
    
    // Utility functions
    function handleFileUpload(event) {
        const files = event.target.files;

        Array.from(files).forEach(file => {
            const ext = file.name.split('.').pop().toLowerCase();
            const isImage = ['jpg', 'jpeg', 'png', 'bmp', 'gif'].includes(ext);

            if (isImage) {
                const reader = new FileReader();
                reader.onload = e => {
                    attachments.push({
                        file: file,
                        url: e.target.result,
                        type: 'image',
                        name: file.name,
                        extension: ext
                    });
                    updateAttachmentPreview();
                };
                reader.readAsDataURL(file);
            } else {
                attachments.push({
                    file: file,
                    type: 'file',
                    name: file.name,
                    extension: ext
                });
                updateAttachmentPreview();
            }
        });

        // Clear file input
        event.target.value = '';
    }

    function updateAttachmentPreview() {
        if (!previewSection) return;

        if (attachments.length === 0) {
            previewSection.style.display = 'none';
            previewSection.innerHTML = '';
            return;
        }

        previewSection.style.display = 'flex';
        previewSection.innerHTML = '';

        attachments.forEach((attachment, index) => {
            const previewItem = document.createElement('div');
            previewItem.className = `preview-item ${attachment.type === 'image' ? 'image-preview' : ''}`;

            if (attachment.type === 'image') {
                previewItem.innerHTML = `
                    <img src="${attachment.url}" alt="${attachment.name}">
                    <button class="remove-attachment" onclick="removeAttachment(${index})">&times;</button>
                `;
            } else {
                previewItem.innerHTML = `
                    <div class="file-preview">
                        <span class="filename">${attachment.name}</span>
                        <span class="extension">${attachment.extension.toUpperCase()}</span>
                    </div>
                    <button class="remove-attachment" onclick="removeAttachment(${index})">&times;</button>
                `;
            }

            previewSection.appendChild(previewItem);
        });
    }

    function removeAttachment(index) {
        attachments.splice(index, 1);
        updateAttachmentPreview();
        updateSendButton();
    }

    function autoResizeTextarea() {
        if (!messageInput) return;
        messageInput.style.height = 'auto';
        messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
    }

    function updateSendButton() {
        if (!sendBtn || !messageInput) return;
        const hasMessage = messageInput.value.trim().length > 0;
        const hasAttachments = attachments.length > 0;
        sendBtn.disabled = (!hasMessage && !hasAttachments) || isTyping;
    }

    function updatePauseButton() {
        const pauseBtn = document.getElementById('pause-agent');
        if (!pauseBtn) return;

        const icon = pauseBtn.querySelector('svg');
        const text = pauseBtn.querySelector('span');

        if (paused) {
            icon.innerHTML = '<path d="M8 5v14l11-7z"></path>';
            text.textContent = 'Resume Agent';
        } else {
            icon.innerHTML = '<path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"></path>';
            text.textContent = 'Pause Agent';
        }
    }

    function switchTab(tabName) {
        const chatsTab = document.getElementById('chats-tab');
        const tasksTab = document.getElementById('tasks-tab');

        if (tabName === 'chats') {
            chatsTab.classList.add('active');
            tasksTab.classList.remove('active');
            chatsSection.style.display = 'block';
            tasksSection.style.display = 'none';
        } else {
            tasksTab.classList.add('active');
            chatsTab.classList.remove('active');
            tasksSection.style.display = 'block';
            chatsSection.style.display = 'none';
        }
    }

    function toggleSidebar(show) {
        if (typeof show === 'boolean') {
            leftPanel.classList.toggle('hidden', !show);
            rightPanel.classList.toggle('expanded', !show);
            document.getElementById('sidebar-overlay').classList.toggle('visible', show && isMobile());
        } else {
            leftPanel.classList.toggle('hidden');
            rightPanel.classList.toggle('expanded');
            const isHidden = leftPanel.classList.contains('hidden');
            document.getElementById('sidebar-overlay').classList.toggle('visible', !isHidden && isMobile());
        }
    }

    function isMobile() {
        return window.innerWidth <= 768;
    }

    function handleResize() {
        if (isMobile()) {
            leftPanel.classList.add('hidden');
            rightPanel.classList.add('expanded');
            document.getElementById('sidebar-overlay').classList.remove('visible');
        } else {
            leftPanel.classList.remove('hidden');
            rightPanel.classList.remove('expanded');
            document.getElementById('sidebar-overlay').classList.remove('visible');
        }
    }

    function updateTime() {
        if (!timeDate) return;
        const now = new Date();
        timeDate.textContent = now.toLocaleString();
    }

    function scrollToBottom() {
        if (!messagesContainer) return;
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    function showWelcomeMessage() {
        if (!messagesContainer) return;
        messagesContainer.innerHTML = `
            <div class="welcome-message">
                <h3>Welcome to Agent Zero!</h3>
                <p>I'm your AI assistant. How can I help you today?</p>
            </div>
        `;
    }

    function setTyping(typing) {
        isTyping = typing;
        updateSendButton();

        // Show/hide typing indicator if it exists
        const indicator = document.getElementById('typing-indicator');
        if (indicator) {
            indicator.style.display = typing ? 'flex' : 'none';
        }

        if (typing && autoScroll) {
            scrollToBottom();
        }
    }

    function clearMessages() {
        messages = [];
        showWelcomeMessage();
    }

    function updateChats(chatsData) {
        chats = chatsData;
        if (!chatsList) return;

        chatsList.innerHTML = '';

        if (chats.length === 0) {
            document.getElementById('empty-chats-message').style.display = 'block';
            return;
        }

        document.getElementById('empty-chats-message').style.display = 'none';

        chats.forEach(chat => {
            const li = document.createElement('li');
            li.innerHTML = `
                <div class="chat-list-button" onclick="selectChat('${chat.id}')">
                    <span class="chat-name" title="${chat.name || 'Chat #' + chat.no}">
                        ${chat.name || 'Chat #' + chat.no}
                    </span>
                </div>
                <button class="edit-button" onclick="deleteChat('${chat.id}')">X</button>
            `;
            chatsList.appendChild(li);
        });
    }

    function updateTasks(tasksData) {
        tasks = tasksData;
        if (!tasksList) return;

        tasksList.innerHTML = '';

        if (tasks.length === 0) {
            document.getElementById('empty-tasks-message').style.display = 'block';
            return;
        }

        document.getElementById('empty-tasks-message').style.display = 'none';

        tasks.forEach(task => {
            const li = document.createElement('li');
            li.innerHTML = `
                <div class="chat-list-button has-task-container" onclick="selectTask('${task.id}')">
                    <div class="task-container task-container-vertical">
                        <span class="task-name" title="${task.title || task.task_name || 'Task #' + task.no}">
                            ${task.title || task.task_name || 'Task #' + task.no}
                        </span>
                        <div class="task-info-line">
                            <span class="scheduler-status-badge scheduler-status-badge-small scheduler-status-${task.status || task.state || 'idle'}">
                                ${task.status || task.state || 'idle'}
                            </span>
                            <button class="edit-button" onclick="openTaskDetail('${task.id}')" title="View task details">
                                ℹ️
                            </button>
                            <button class="edit-button" onclick="runTask('${task.id}')" title="Run task">
                                ▶️
                            </button>
                            <button class="edit-button" onclick="deleteTask('${task.id}')" title="Delete task">❌</button>
                        </div>
                    </div>
                </div>
            `;
            tasksList.appendChild(li);
        });
    }

    // Task Management Functions
    function createNewTask() {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-container">
                <div class="modal-header">
                    <h2>Create New Task</h2>
                    <button class="modal-close" onclick="closeTaskModal()">&times;</button>
                </div>
                <div class="modal-content">
                    <form class="task-creation-form" id="task-form">
                        <div class="task-form-group">
                            <label class="task-form-label">Task Title</label>
                            <input type="text" class="task-form-input" id="task-title" placeholder="Enter task title" required>
                        </div>

                        <div class="task-form-group">
                            <label class="task-form-label">Description</label>
                            <textarea class="task-form-textarea" id="task-description" placeholder="Describe what this task should do" required></textarea>
                        </div>

                        <div class="task-form-group">
                            <label class="task-form-label">Priority</label>
                            <div class="task-priority-selector">
                                <div class="task-priority-option task-priority-low" data-priority="low">Low</div>
                                <div class="task-priority-option task-priority-medium selected" data-priority="medium">Medium</div>
                                <div class="task-priority-option task-priority-high" data-priority="high">High</div>
                                <div class="task-priority-option task-priority-urgent" data-priority="urgent">Urgent</div>
                            </div>
                        </div>

                        <div class="task-form-group">
                            <label class="task-form-label">Task Type</label>
                            <select class="task-form-select" id="task-type">
                                <option value="immediate">Immediate</option>
                                <option value="scheduled">Scheduled</option>
                                <option value="planned">Planned</option>
                            </select>
                        </div>

                        <div class="task-form-group" id="schedule-options" style="display: none;">
                            <label class="task-form-label">Schedule</label>
                            <input type="datetime-local" class="task-form-input" id="task-schedule">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="modal-btn modal-btn-secondary" onclick="closeTaskModal()">Cancel</button>
                    <button class="modal-btn modal-btn-primary" onclick="submitTask()">Create Task</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Setup priority selector
        modal.querySelectorAll('.task-priority-option').forEach(option => {
            option.addEventListener('click', () => {
                modal.querySelectorAll('.task-priority-option').forEach(opt => opt.classList.remove('selected'));
                option.classList.add('selected');
            });
        });

        // Setup task type change
        const taskTypeSelect = modal.querySelector('#task-type');
        const scheduleOptions = modal.querySelector('#schedule-options');
        taskTypeSelect.addEventListener('change', () => {
            scheduleOptions.style.display = taskTypeSelect.value === 'scheduled' ? 'block' : 'none';
        });

        // Close modal on overlay click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeTaskModal();
            }
        });
    }

    function closeTaskModal() {
        const modal = document.querySelector('.modal-overlay:last-child');
        if (modal) {
            document.body.removeChild(modal);
        }
    }

    function submitTask() {
        const form = document.getElementById('task-form');
        const title = form.querySelector('#task-title').value;
        const description = form.querySelector('#task-description').value;
        const priority = form.querySelector('.task-priority-option.selected').dataset.priority;
        const type = form.querySelector('#task-type').value;
        const schedule = form.querySelector('#task-schedule').value;

        if (!title || !description) {
            alert('Please fill in all required fields');
            return;
        }

        const taskData = {
            title,
            description,
            priority,
            type,
            schedule: type === 'scheduled' ? schedule : null
        };

        vscode.postMessage({
            type: 'createTask',
            task: taskData
        });

        closeTaskModal();
    }

    function openTaskDetail(taskId) {
        vscode.postMessage({
            type: 'getTaskDetail',
            taskId: taskId
        });
    }

    function showTaskDetail(task) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-container">
                <div class="modal-header">
                    <h2>Task Details</h2>
                    <button class="modal-close" onclick="closeTaskModal()">&times;</button>
                </div>
                <div class="modal-content">
                    <div class="task-detail">
                        <div class="task-detail-header">
                            <h3 class="task-detail-title">${task.title}</h3>
                            <div class="task-detail-actions">
                                <button class="task-action-btn primary" onclick="runTask('${task.id}')">Run</button>
                                <button class="task-action-btn" onclick="editTask('${task.id}')">Edit</button>
                                <button class="task-action-btn danger" onclick="deleteTask('${task.id}')">Delete</button>
                            </div>
                        </div>

                        <div class="task-detail-info">
                            <div class="task-info-item">
                                <span class="task-info-label">Status</span>
                                <span class="task-info-value">
                                    <span class="scheduler-status-badge scheduler-status-${task.status}">${task.status}</span>
                                </span>
                            </div>
                            <div class="task-info-item">
                                <span class="task-info-label">Priority</span>
                                <span class="task-info-value">${task.priority}</span>
                            </div>
                            <div class="task-info-item">
                                <span class="task-info-label">Created</span>
                                <span class="task-info-value">${formatTimestamp(task.createdAt)}</span>
                            </div>
                            <div class="task-info-item">
                                <span class="task-info-label">Last Updated</span>
                                <span class="task-info-value">${formatTimestamp(task.updatedAt)}</span>
                            </div>
                        </div>

                        <div class="task-description">
                            <strong>Description:</strong><br>
                            ${task.description}
                        </div>

                        ${task.result ? `
                            <div class="task-result">
                                <strong>Result:</strong><br>
                                ${task.result}
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Close modal on overlay click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeTaskModal();
            }
        });
    }

    function runTask(taskId) {
        vscode.postMessage({
            type: 'runTask',
            taskId: taskId
        });
    }

    function editTask(taskId) {
        vscode.postMessage({
            type: 'editTask',
            taskId: taskId
        });
    }

    function deleteTask(taskId) {
        if (confirm('Are you sure you want to delete this task?')) {
            vscode.postMessage({
                type: 'deleteTask',
                taskId: taskId
            });
        }
    }

    // Advanced Features
    function openSearchModal() {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-container">
                <div class="modal-header">
                    <h2>Search Chat History</h2>
                    <button class="modal-close" onclick="closeSearchModal()">&times;</button>
                </div>
                <div class="modal-content">
                    <div class="search-form">
                        <div class="search-input-group">
                            <input type="text" id="search-query" placeholder="Enter search terms..." class="search-input">
                            <button class="search-btn" onclick="performSearch()">Search</button>
                        </div>
                        <div class="search-filters">
                            <label>
                                <input type="checkbox" id="search-messages" checked> Messages
                            </label>
                            <label>
                                <input type="checkbox" id="search-files"> Files
                            </label>
                            <label>
                                <input type="checkbox" id="search-tasks"> Tasks
                            </label>
                        </div>
                    </div>
                    <div id="search-results" class="search-results">
                        <!-- Search results will appear here -->
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Focus on search input
        modal.querySelector('#search-query').focus();

        // Handle Enter key
        modal.querySelector('#search-query').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // Close modal on overlay click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeSearchModal();
            }
        });
    }

    function closeSearchModal() {
        const modal = document.querySelector('.modal-overlay:last-child');
        if (modal) {
            document.body.removeChild(modal);
        }
    }

    function performSearch() {
        const query = document.getElementById('search-query').value;
        const searchMessages = document.getElementById('search-messages').checked;
        const searchFiles = document.getElementById('search-files').checked;
        const searchTasks = document.getElementById('search-tasks').checked;

        if (!query.trim()) {
            alert('Please enter a search query');
            return;
        }

        vscode.postMessage({
            type: 'performSearch',
            query: query,
            filters: {
                messages: searchMessages,
                files: searchFiles,
                tasks: searchTasks
            }
        });
    }

    function displaySearchResults(results) {
        const resultsContainer = document.getElementById('search-results');
        if (!resultsContainer) return;

        resultsContainer.innerHTML = '';

        if (results.length === 0) {
            resultsContainer.innerHTML = '<div class="search-no-results">No results found</div>';
            return;
        }

        results.forEach(result => {
            const resultItem = document.createElement('div');
            resultItem.className = 'search-result-item';
            resultItem.innerHTML = `
                <div class="search-result-type">${result.type}</div>
                <div class="search-result-title">${result.title}</div>
                <div class="search-result-snippet">${result.snippet}</div>
                <div class="search-result-date">${formatTimestamp(result.timestamp)}</div>
            `;

            resultItem.addEventListener('click', () => {
                vscode.postMessage({
                    type: 'openSearchResult',
                    result: result
                });
                closeSearchModal();
            });

            resultsContainer.appendChild(resultItem);
        });
    }

    function openMemoryModal() {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-container">
                <div class="modal-header">
                    <h2>Memory Management</h2>
                    <button class="modal-close" onclick="closeMemoryModal()">&times;</button>
                </div>
                <div class="modal-content">
                    <div class="memory-tabs">
                        <button class="memory-tab active" data-tab="view">View Memories</button>
                        <button class="memory-tab" data-tab="add">Add Memory</button>
                        <button class="memory-tab" data-tab="search">Search Memories</button>
                    </div>

                    <div id="memory-view" class="memory-tab-content">
                        <div id="memories-list" class="memories-list">
                            <!-- Memories will be loaded here -->
                        </div>
                    </div>

                    <div id="memory-add" class="memory-tab-content" style="display: none;">
                        <div class="memory-form">
                            <div class="form-group">
                                <label>Memory Title</label>
                                <input type="text" id="memory-title" placeholder="Enter memory title">
                            </div>
                            <div class="form-group">
                                <label>Memory Content</label>
                                <textarea id="memory-content" placeholder="Enter memory content" rows="5"></textarea>
                            </div>
                            <div class="form-group">
                                <label>Tags (comma separated)</label>
                                <input type="text" id="memory-tags" placeholder="tag1, tag2, tag3">
                            </div>
                            <div class="form-group">
                                <label>Importance</label>
                                <select id="memory-importance">
                                    <option value="low">Low</option>
                                    <option value="medium" selected>Medium</option>
                                    <option value="high">High</option>
                                </select>
                            </div>
                            <button class="btn btn-primary" onclick="saveMemory()">Save Memory</button>
                        </div>
                    </div>

                    <div id="memory-search" class="memory-tab-content" style="display: none;">
                        <div class="memory-search-form">
                            <input type="text" id="memory-search-query" placeholder="Search memories...">
                            <button class="btn btn-primary" onclick="searchMemories()">Search</button>
                        </div>
                        <div id="memory-search-results" class="memory-search-results">
                            <!-- Search results will appear here -->
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Setup memory tabs
        modal.querySelectorAll('.memory-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                const tabName = tab.dataset.tab;
                switchMemoryTab(tabName);
            });
        });

        // Load memories
        loadMemories();

        // Close modal on overlay click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeMemoryModal();
            }
        });
    }

    function closeMemoryModal() {
        const modal = document.querySelector('.modal-overlay:last-child');
        if (modal) {
            document.body.removeChild(modal);
        }
    }

    function switchMemoryTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.memory-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.memory-tab-content').forEach(content => {
            content.style.display = 'none';
        });
        document.getElementById(`memory-${tabName}`).style.display = 'block';
    }

    function loadMemories() {
        vscode.postMessage({
            type: 'getMemories'
        });
    }

    function saveMemory() {
        const title = document.getElementById('memory-title').value;
        const content = document.getElementById('memory-content').value;
        const tags = document.getElementById('memory-tags').value.split(',').map(tag => tag.trim());
        const importance = document.getElementById('memory-importance').value;

        if (!title || !content) {
            alert('Please fill in title and content');
            return;
        }

        vscode.postMessage({
            type: 'saveMemory',
            memory: {
                title,
                content,
                tags,
                importance
            }
        });

        // Clear form
        document.getElementById('memory-title').value = '';
        document.getElementById('memory-content').value = '';
        document.getElementById('memory-tags').value = '';
        document.getElementById('memory-importance').value = 'medium';
    }

    function searchMemories() {
        const query = document.getElementById('memory-search-query').value;
        if (!query.trim()) {
            alert('Please enter a search query');
            return;
        }

        vscode.postMessage({
            type: 'searchMemories',
            query: query
        });
    }

    function displayMemories(memories) {
        const memoriesList = document.getElementById('memories-list');
        if (!memoriesList) return;

        memoriesList.innerHTML = '';

        if (memories.length === 0) {
            memoriesList.innerHTML = '<div class="no-memories">No memories found</div>';
            return;
        }

        memories.forEach(memory => {
            const memoryItem = document.createElement('div');
            memoryItem.className = 'memory-item';
            memoryItem.innerHTML = `
                <div class="memory-header">
                    <span class="memory-title">${memory.title}</span>
                    <span class="memory-importance memory-importance-${memory.importance}">${memory.importance}</span>
                </div>
                <div class="memory-content">${memory.content}</div>
                <div class="memory-tags">
                    ${memory.tags.map(tag => `<span class="memory-tag">${tag}</span>`).join('')}
                </div>
                <div class="memory-date">${formatTimestamp(memory.timestamp)}</div>
            `;

            memoriesList.appendChild(memoryItem);
        });
    }

    function displayMemorySearchResults(results) {
        const searchResults = document.getElementById('memory-search-results');
        if (!searchResults) return;

        searchResults.innerHTML = '';

        if (results.length === 0) {
            searchResults.innerHTML = '<div class="no-memories">No memories found</div>';
            return;
        }

        results.forEach(memory => {
            const memoryItem = document.createElement('div');
            memoryItem.className = 'memory-item';
            memoryItem.innerHTML = `
                <div class="memory-header">
                    <span class="memory-title">${memory.title}</span>
                    <span class="memory-importance memory-importance-${memory.importance}">${memory.importance}</span>
                </div>
                <div class="memory-content">${memory.content}</div>
                <div class="memory-tags">
                    ${memory.tags.map(tag => `<span class="memory-tag">${tag}</span>`).join('')}
                </div>
                <div class="memory-date">${formatTimestamp(memory.timestamp)}</div>
            `;

            searchResults.appendChild(memoryItem);
        });
    }

    // Global functions for onclick handlers
    window.removeAttachment = removeAttachment;
    window.selectChat = (chatId) => vscode.postMessage({ type: 'selectChat', chatId });
    window.deleteChat = (chatId) => vscode.postMessage({ type: 'deleteChat', chatId });
    window.selectTask = (taskId) => vscode.postMessage({ type: 'selectTask', taskId });
    window.deleteTask = deleteTask;
    window.openTaskDetail = openTaskDetail;
    window.runTask = runTask;
    window.editTask = editTask;
    window.createNewTask = createNewTask;
    window.closeTaskModal = closeTaskModal;
    window.submitTask = submitTask;

    // Handle messages from extension
    function handleExtensionMessage(event) {
        const message = event.data;

        switch (message.type) {
            case 'addMessage':
                addMessage(message.message);
                break;
            case 'setTyping':
                setTyping(message.isTyping);
                break;
            case 'clearMessages':
                clearMessages();
                break;
            case 'loadMessages':
                clearMessages();
                message.messages.forEach(msg => addMessage(msg));
                break;
            case 'updateChats':
                updateChats(message.chats);
                break;
            case 'updateTasks':
                updateTasks(message.tasks);
                break;
            case 'updateConnectionStatus':
                connectionStatus = message.connected;
                updateConnectionIndicator();
                break;
            case 'showToast':
                showToast(message.toast);
                break;
            case 'settingsData':
                renderSettingsContent(message.sections);
                break;
            case 'settingsTabData':
                renderSettingsContent(message.sections);
                break;
            case 'fileList':
                renderFileList(message.files, message.path);
                break;
            case 'chatHistory':
                renderChatHistory(message.history);
                break;
            case 'showTaskDetail':
                showTaskDetail(message.task);
                break;
            case 'searchResults':
                displaySearchResults(message.results);
                break;
            case 'memoriesData':
                displayMemories(message.memories);
                break;
            case 'memorySearchResults':
                displayMemorySearchResults(message.results);
                break;
        }
    }

    function updateConnectionIndicator() {
        const connectedCircle = document.querySelector('.connected-circle');
        const disconnectedCircle = document.querySelector('.disconnected-circle');

        if (connectedCircle) {
            connectedCircle.style.opacity = connectionStatus ? '1' : '0';
        }
        if (disconnectedCircle) {
            disconnectedCircle.style.opacity = connectionStatus ? '0' : '1';
        }
    }

    function showToast(toast) {
        // Simple toast implementation
        const toastEl = document.getElementById('toast');
        if (!toastEl) return;

        toastEl.querySelector('.toast__title').textContent = toast.title || '';
        toastEl.querySelector('.toast__message').textContent = toast.message || '';
        toastEl.className = `toast ${toast.type || 'info'}`;
        toastEl.style.display = 'block';

        // Auto-hide after 5 seconds
        setTimeout(() => {
            toastEl.style.display = 'none';
        }, 5000);
    }

    // Settings Modal Functions
    function openSettingsModal() {
        const modal = document.getElementById('settings-modal');
        if (modal) {
            modal.style.display = 'flex';
            loadSettings();
        }
    }

    function closeSettingsModal() {
        const modal = document.getElementById('settings-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    function loadSettings() {
        vscode.postMessage({ type: 'getSettings' });
    }

    function saveSettings() {
        const settingsData = collectSettingsData();
        vscode.postMessage({
            type: 'saveSettings',
            settings: settingsData
        });
    }

    function collectSettingsData() {
        const settings = {};
        const inputs = document.querySelectorAll('#settings-content input, #settings-content select, #settings-content textarea');

        inputs.forEach(input => {
            if (input.type === 'checkbox') {
                settings[input.id] = input.checked;
            } else {
                settings[input.id] = input.value;
            }
        });

        return settings;
    }

    function switchSettingsTab(tabName) {
        // Update tab appearance
        document.querySelectorAll('.settings-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Load tab content
        vscode.postMessage({
            type: 'getSettingsTab',
            tab: tabName
        });
    }

    function renderSettingsContent(sections) {
        const content = document.getElementById('settings-content');
        if (!content) return;

        content.innerHTML = '';

        sections.forEach((section, sectionIndex) => {
            const sectionDiv = document.createElement('div');
            sectionDiv.className = 'section';
            sectionDiv.id = `section${sectionIndex + 1}`;

            sectionDiv.innerHTML = `
                <div class="section-title">${section.title}</div>
                <div class="section-description">${section.description || ''}</div>
            `;

            section.fields.forEach(field => {
                if (field.hidden) return;

                const fieldDiv = document.createElement('div');
                fieldDiv.className = field.type === 'textarea' ? 'field field-full' : 'field';

                fieldDiv.innerHTML = `
                    <div class="field-label">
                        <div class="field-title">${field.title}</div>
                        <div class="field-description">${field.description || ''}</div>
                    </div>
                    <div class="field-control">
                        ${renderFieldControl(field)}
                    </div>
                `;

                sectionDiv.appendChild(fieldDiv);
            });

            content.appendChild(sectionDiv);
        });
    }

    function renderFieldControl(field) {
        switch (field.type) {
            case 'text':
                return `<input type="text" id="${field.id}" value="${field.value || ''}" ${field.readonly ? 'readonly' : ''}>`;
            case 'password':
                return `<input type="password" id="${field.id}" value="${field.value || ''}" ${field.readonly ? 'readonly' : ''}>`;
            case 'number':
                return `<input type="number" id="${field.id}" value="${field.value || ''}"
                        min="${field.min || ''}" max="${field.max || ''}" step="${field.step || ''}"
                        ${field.readonly ? 'readonly' : ''}>`;
            case 'textarea':
                return `<textarea id="${field.id}" ${field.readonly ? 'readonly' : ''}>${field.value || ''}</textarea>`;
            case 'switch':
                return `<label class="toggle">
                            <input type="checkbox" id="${field.id}" ${field.value ? 'checked' : ''} ${field.readonly ? 'disabled' : ''}>
                            <span class="toggler"></span>
                        </label>`;
            case 'select':
                const options = field.options.map(opt =>
                    `<option value="${opt.value}" ${opt.value === field.value ? 'selected' : ''}>${opt.label}</option>`
                ).join('');
                return `<select id="${field.id}" ${field.readonly ? 'disabled' : ''}>${options}</select>`;
            case 'range':
                return `<input type="range" id="${field.id}" value="${field.value || ''}"
                        min="${field.min || ''}" max="${field.max || ''}" step="${field.step || ''}"
                        ${field.readonly ? 'disabled' : ''}>
                        <span class="range-value">${field.value || ''}</span>`;
            case 'button':
                return `<button class="btn btn-field" id="${field.id}" ${field.readonly ? 'disabled' : ''}>${field.value}</button>`;
            default:
                return `<input type="text" id="${field.id}" value="${field.value || ''}" ${field.readonly ? 'readonly' : ''}>`;
        }
    }

    // File Browser Functions
    function openFileBrowser() {
        const modal = document.getElementById('file-browser-modal');
        if (modal) {
            modal.style.display = 'flex';
            vscode.postMessage({ type: 'getFileList', path: '/' });
        }
    }

    function closeFileBrowser() {
        const modal = document.getElementById('file-browser-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // History Functions
    function openHistory() {
        const modal = document.getElementById('history-modal');
        if (modal) {
            modal.style.display = 'flex';
            vscode.postMessage({ type: 'getChatHistory' });
        }
    }

    function closeHistory() {
        const modal = document.getElementById('history-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // Setup modal event listeners
    function setupModals() {
        // Settings modal
        const settingsClose = document.getElementById('settings-close');
        const settingsCancel = document.getElementById('settings-cancel');
        const settingsSave = document.getElementById('settings-save');

        if (settingsClose) settingsClose.addEventListener('click', closeSettingsModal);
        if (settingsCancel) settingsCancel.addEventListener('click', closeSettingsModal);
        if (settingsSave) settingsSave.addEventListener('click', saveSettings);

        // Settings tabs
        document.querySelectorAll('.settings-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                switchSettingsTab(tab.dataset.tab);
            });
        });

        // File browser modal
        const fileBrowserClose = document.getElementById('file-browser-close');
        if (fileBrowserClose) fileBrowserClose.addEventListener('click', closeFileBrowser);

        // History modal
        const historyClose = document.getElementById('history-close');
        if (historyClose) historyClose.addEventListener('click', closeHistory);

        // Close modals on overlay click
        document.querySelectorAll('.modal-overlay').forEach(overlay => {
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    overlay.style.display = 'none';
                }
            });
        });
    }

    function renderFileList(files, currentPath) {
        const fileList = document.getElementById('file-browser-list');
        const pathElement = document.getElementById('file-browser-path');

        if (pathElement) {
            pathElement.textContent = currentPath;
        }

        if (!fileList) return;

        fileList.innerHTML = '';

        if (files.length === 0) {
            fileList.innerHTML = '<div class="file-browser-empty">No files found</div>';
            return;
        }

        // Add parent directory option if not at root
        if (currentPath !== '/' && currentPath !== '') {
            const parentItem = document.createElement('div');
            parentItem.className = 'file-item';
            parentItem.innerHTML = `
                <div class="file-icon">⬆️</div>
                <div class="file-name">..</div>
                <div class="file-size"></div>
            `;

            parentItem.addEventListener('click', () => {
                const parentPath = currentPath.split('/').slice(0, -1).join('/') || '/';
                vscode.postMessage({
                    type: 'getFileList',
                    path: parentPath
                });
            });

            fileList.appendChild(parentItem);
        }

        // Sort files: directories first, then files
        const sortedFiles = files.sort((a, b) => {
            if (a.isDirectory && !b.isDirectory) return -1;
            if (!a.isDirectory && b.isDirectory) return 1;
            return a.name.localeCompare(b.name);
        });

        sortedFiles.forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';

            // Get appropriate icon
            let icon = '📄';
            if (file.isDirectory) {
                icon = '📁';
            } else {
                const ext = file.name.split('.').pop().toLowerCase();
                switch (ext) {
                    case 'js':
                    case 'ts':
                        icon = '📜';
                        break;
                    case 'json':
                        icon = '⚙️';
                        break;
                    case 'md':
                        icon = '📝';
                        break;
                    case 'png':
                    case 'jpg':
                    case 'jpeg':
                    case 'gif':
                        icon = '🖼️';
                        break;
                    case 'css':
                        icon = '🎨';
                        break;
                    case 'html':
                        icon = '🌐';
                        break;
                    default:
                        icon = '📄';
                }
            }

            fileItem.innerHTML = `
                <div class="file-icon">${icon}</div>
                <div class="file-name" title="${file.name}">${file.name}</div>
                <div class="file-size">${file.isDirectory ? '' : (file.size || '')}</div>
            `;

            fileItem.addEventListener('click', () => {
                if (file.isDirectory) {
                    vscode.postMessage({
                        type: 'getFileList',
                        path: file.path
                    });
                } else {
                    vscode.postMessage({
                        type: 'openFile',
                        path: file.path
                    });
                }
            });

            // Add context menu for files
            fileItem.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                showFileContextMenu(e, file);
            });

            fileList.appendChild(fileItem);
        });
    }

    function showFileContextMenu(event, file) {
        // Simple context menu implementation
        const menu = document.createElement('div');
        menu.className = 'context-menu';
        menu.style.position = 'fixed';
        menu.style.left = event.clientX + 'px';
        menu.style.top = event.clientY + 'px';
        menu.style.backgroundColor = 'var(--vscode-menu-background)';
        menu.style.border = '1px solid var(--vscode-menu-border)';
        menu.style.borderRadius = '4px';
        menu.style.padding = '4px 0';
        menu.style.zIndex = '10000';
        menu.style.minWidth = '120px';

        const actions = [
            { label: 'Open', action: () => vscode.postMessage({ type: 'openFile', path: file.path }) },
            { label: 'Copy Path', action: () => navigator.clipboard.writeText(file.path) }
        ];

        if (!file.isDirectory) {
            actions.push({ label: 'Insert to Chat', action: () => insertFileToChat(file) });
        }

        actions.forEach(action => {
            const item = document.createElement('div');
            item.textContent = action.label;
            item.style.padding = '6px 12px';
            item.style.cursor = 'pointer';
            item.style.fontSize = 'var(--font-size-small)';
            item.addEventListener('click', () => {
                action.action();
                document.body.removeChild(menu);
            });
            item.addEventListener('mouseenter', () => {
                item.style.backgroundColor = 'var(--vscode-menu-selectionBackground)';
            });
            item.addEventListener('mouseleave', () => {
                item.style.backgroundColor = 'transparent';
            });
            menu.appendChild(item);
        });

        document.body.appendChild(menu);

        // Remove menu when clicking elsewhere
        const removeMenu = (e) => {
            if (!menu.contains(e.target)) {
                document.body.removeChild(menu);
                document.removeEventListener('click', removeMenu);
            }
        };
        setTimeout(() => document.addEventListener('click', removeMenu), 0);
    }

    function insertFileToChat(file) {
        if (messageInput) {
            const currentValue = messageInput.value;
            const fileReference = `[File: ${file.name}](${file.path})`;
            messageInput.value = currentValue + (currentValue ? '\n' : '') + fileReference;
            autoResizeTextarea();
            updateSendButton();
        }
        closeFileBrowser();
    }

    function setupFileBrowserToolbar() {
        const backBtn = document.getElementById('file-browser-back');
        const upBtn = document.getElementById('file-browser-up');
        const refreshBtn = document.getElementById('file-browser-refresh');

        if (backBtn) {
            backBtn.addEventListener('click', () => {
                // Implement back functionality
                vscode.postMessage({ type: 'fileBrowserBack' });
            });
        }

        if (upBtn) {
            upBtn.addEventListener('click', () => {
                const pathElement = document.getElementById('file-browser-path');
                if (pathElement) {
                    const currentPath = pathElement.textContent;
                    const parentPath = currentPath.split('/').slice(0, -1).join('/') || '/';
                    vscode.postMessage({
                        type: 'getFileList',
                        path: parentPath
                    });
                }
            });
        }

        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                const pathElement = document.getElementById('file-browser-path');
                if (pathElement) {
                    vscode.postMessage({
                        type: 'getFileList',
                        path: pathElement.textContent
                    });
                }
            });
        }
    }

    function renderChatHistory(history) {
        const historyList = document.getElementById('history-list');
        if (!historyList) return;

        historyList.innerHTML = '';

        history.forEach(item => {
            const historyItem = document.createElement('li');
            historyItem.className = 'history-item';
            historyItem.innerHTML = `
                <div class="history-timestamp">${formatTimestamp(item.timestamp)}</div>
                <div class="history-preview">${item.preview}</div>
            `;

            historyItem.addEventListener('click', () => {
                vscode.postMessage({
                    type: 'loadChatHistory',
                    historyId: item.id
                });
                closeHistory();
            });

            historyList.appendChild(historyItem);
        });
    }

    // Add to initialization
    document.addEventListener('DOMContentLoaded', function() {
        // ... existing initialization code ...
        setupModals();
        setupFileBrowserToolbar();
    });

    // Global functions for modal access
    window.openSettingsModal = openSettingsModal;
    window.openFileBrowser = openFileBrowser;
    window.openHistory = openHistory;
    window.openSearchModal = openSearchModal;
    window.closeSearchModal = closeSearchModal;
    window.performSearch = performSearch;
    window.openMemoryModal = openMemoryModal;
    window.closeMemoryModal = closeMemoryModal;
    window.saveMemory = saveMemory;
    window.searchMemories = searchMemories;

})();
