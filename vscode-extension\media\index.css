/* Agent Zero VS Code Extension - Main CSS */

/* CSS Variables adapted for VS Code */
:root {
  /* Use VS Code theme variables where possible */
  --color-background: var(--vscode-editor-background);
  --color-text: var(--vscode-foreground);
  --color-primary: var(--vscode-textLink-foreground);
  --color-secondary: var(--vscode-button-secondaryBackground);
  --color-accent: var(--vscode-textLink-activeForeground);
  --color-message-bg: var(--vscode-editor-inactiveSelectionBackground);
  --color-message-text: var(--vscode-foreground);
  --color-panel: var(--vscode-sideBar-background);
  --color-border: var(--vscode-panel-border);
  --color-input: var(--vscode-input-background);
  --color-input-focus: var(--vscode-input-background);

  /* Spacing variables */
  --spacing-xxs: 0.15rem;
  --spacing-xs: 0.3125rem;
  --spacing-sm: 0.625rem;
  --spacing-md: 1.25rem;
  --spacing-lg: 2rem;

  /* Font sizes */
  --font-size-small: 0.8rem;
  --font-size-smaller: 0.9rem;
  --font-size-normal: 1rem;
  --font-size-large: 1.2rem;

  /* Other variables */
  --border-radius: 1.125rem;
  --transition-speed: 0.3s;
}

/* Reset and Base Styles */
body,
html {
  background-color: var(--color-background);
  color: var(--color-text);
  font-family: var(--vscode-font-family);
  font-size: var(--vscode-font-size);
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  position: fixed;
}

body {
  overscroll-behavior: none;
  -webkit-overscroll-behavior: none;
}

/* Layout */
.container {
  display: flex;
  height: 100vh;
  width: 100vw;
}

.panel {
  display: flex;
  height: 100%;
  overflow: auto;
  scroll-behavior: smooth;
}

/* Left Panel (Sidebar) */
#left-panel {
  background-color: var(--color-panel);
  border-right: 1px solid var(--color-border);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: all var(--transition-speed) ease-in-out;
  width: 250px;
  min-width: 250px;
  color: var(--color-text);
  box-shadow: 1px 0 5px rgba(0, 0, 0, 0.1);
  user-select: none;
}

#left-panel.hidden {
  margin-left: -250px;
}

.left-panel-top {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
  margin-top: 3.5rem;
  padding: var(--spacing-md) var(--spacing-md) 0 var(--spacing-md);
}

.left-panel-bottom {
  position: relative;
  flex-shrink: 0;
  padding: var(--spacing-md);
}

/* Right Panel (Chat Area) */
#right-panel {
  flex: 1;
  background-color: var(--color-background);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

#right-panel.expanded {
  width: 100%;
}

/* Sidebar Toggle Button */
.icons-section {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1004;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
}

.toggle-sidebar-button {
  height: 2.6rem;
  width: 2.6rem;
  background-color: var(--color-background);
  border: 0.1rem solid var(--color-border);
  border-radius: var(--spacing-xs);
  color: var(--color-text);
  opacity: 0.8;
  cursor: pointer;
  padding: 0.47rem 0.56rem;
  transition: all var(--transition-speed) ease-in-out;
}

.toggle-sidebar-button:hover {
  background-color: var(--vscode-button-hoverBackground);
  opacity: 1;
}

#logo-container img {
  border-radius: 4px;
}

/* Config Buttons */
.config-section {
  margin-bottom: var(--spacing-md);
}

.config-button {
  display: block;
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  margin-bottom: var(--spacing-xs);
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: 1px solid var(--vscode-button-border);
  border-radius: 4px;
  cursor: pointer;
  font-size: var(--font-size-small);
  transition: background-color 0.2s;
}

.config-button:hover {
  background-color: var(--vscode-button-hoverBackground);
}

/* Tabs */
.tabs-container {
  margin-bottom: var(--spacing-md);
}

.tabs {
  display: flex;
  border-bottom: 1px solid var(--color-border);
}

.tab {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  text-align: center;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
  font-size: var(--font-size-small);
}

.tab.active {
  border-bottom-color: var(--color-primary);
  color: var(--color-primary);
}

.tab:hover {
  background-color: var(--vscode-list-hoverBackground);
}

/* Lists */
.config-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.config-list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid var(--color-border);
}

.config-list li:last-child {
  border-bottom: none;
}

/* Chat List */
.chats-list-container,
.tasks-list-container {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  scroll-behavior: smooth;
}

.chat-list-button {
  display: block;
  width: 100%;
  padding: 8px 5px;
  cursor: pointer;
  overflow: hidden;
  position: relative;
  border-radius: 4px;
  transition: background-color 0.2s ease-in-out;
  text-align: left;
  background: none;
  border: none;
  color: inherit;
}

.chat-list-button:hover {
  background-color: var(--vscode-list-hoverBackground);
}

.chat-name {
  display: inline-block;
  max-width: 160px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: var(--font-size-small);
}

/* Edit Button */
.edit-button {
  background: var(--vscode-button-secondaryBackground);
  border: 1px solid var(--vscode-button-border);
  color: var(--vscode-button-secondaryForeground);
  padding: 2px 6px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 10px;
  margin-left: 5px;
}

.edit-button:hover {
  background-color: var(--vscode-button-secondaryHoverBackground);
}

/* Empty List Message */
.empty-list-message {
  text-align: center;
  padding: var(--spacing-lg);
  color: var(--vscode-descriptionForeground);
  font-style: italic;
}

/* Preferences */
.pref-section {
  border-top: 1px solid var(--color-border);
  padding-top: var(--spacing-md);
}

.pref-header {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--font-size-normal);
  color: var(--color-text);
}

/* Switch */
.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--vscode-checkbox-background);
  transition: 0.4s;
  border-radius: 20px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--vscode-checkbox-selectBackground);
}

input:checked + .slider:before {
  transform: translateX(20px);
}

/* Version Info */
.version-info {
  text-align: center;
  padding: var(--spacing-sm);
  font-size: var(--font-size-small);
  color: var(--vscode-descriptionForeground);
  border-top: 1px solid var(--color-border);
}
