/* Messages CSS for Agent Zero VS Code Extension */

/* Chat History */
#chat-history {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  scroll-behavior: auto;
  padding: var(--spacing-md) var(--spacing-sm) 0;
  transition: all 0.3s ease;
}

#chat-history > *:first-child {
  margin-top: 4.4em;
}

/* Scrollbar styling */
#chat-history::-webkit-scrollbar {
  width: 8px;
}

#chat-history::-webkit-scrollbar-track {
  background: var(--vscode-scrollbarSlider-background);
}

#chat-history::-webkit-scrollbar-thumb {
  background: var(--vscode-scrollbarSlider-background);
  border-radius: 4px;
}

#chat-history::-webkit-scrollbar-thumb:hover {
  background: var(--vscode-scrollbarSlider-hoverBackground);
}

/* Message Containers */
.user-container {
  align-self: flex-end;
  display: flex;
  justify-content: flex-end;
  width: 100%;
  margin-bottom: var(--spacing-sm);
}

.ai-container {
  align-self: flex-start;
  margin-bottom: var(--spacing-sm);
}

.center-container {
  align-self: center;
  max-width: 80%;
  margin: 0 0 var(--spacing-sm) 0;
}

/* Message Styles */
.message {
  max-width: 85%;
  word-wrap: break-word;
  animation: fadeIn 0.3s ease-in;
  border-radius: 12px;
  padding: 10px 15px;
  position: relative;
}

.message-body {
  padding-top: 0.5em;
  padding-bottom: 0.5em;
}

.message-user {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border-bottom-right-radius: 4px;
  min-width: 195px;
  text-align: end;
}

.message-user > div {
  padding-top: var(--spacing-xs);
  font-family: var(--vscode-editor-font-family);
  font-size: var(--font-size-small);
}

.message-ai {
  background-color: var(--vscode-editor-inactiveSelectionBackground);
  border: 1px solid var(--vscode-panel-border);
  border-bottom-left-radius: 4px;
  color: var(--vscode-foreground);
}

.message-center {
  align-self: center;
  background-color: var(--vscode-inputValidation-infoBackground);
  color: var(--vscode-inputValidation-infoForeground);
  border: 1px solid var(--vscode-inputValidation-infoBorder);
  text-align: center;
  font-style: italic;
}

/* Message Header */
.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-small);
  opacity: 0.8;
}

.message-sender {
  font-weight: bold;
  color: var(--vscode-textLink-foreground);
}

.message-timestamp {
  font-size: 10px;
  opacity: 0.6;
}

/* Message Content */
.message-content {
  line-height: 1.5;
}

.message-content p {
  margin: 0 0 var(--spacing-xs) 0;
}

.message-content p:last-child {
  margin-bottom: 0;
}

/* Code blocks in messages */
.message-content pre {
  background-color: var(--vscode-textCodeBlock-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  padding: 10px;
  margin: 10px 0;
  overflow-x: auto;
  font-family: var(--vscode-editor-font-family);
  font-size: var(--vscode-editor-font-size);
}

.message-content code {
  background-color: var(--vscode-textCodeBlock-background);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: var(--vscode-editor-font-family);
}

/* Links in messages */
.message-content a {
  color: var(--vscode-textLink-foreground);
  text-decoration: none;
}

.message-content a:hover {
  text-decoration: underline;
}

/* Welcome Message */
.welcome-message {
  text-align: center;
  padding: 20px;
  background-color: var(--vscode-editor-inactiveSelectionBackground);
  border-radius: 8px;
  border: 1px solid var(--vscode-panel-border);
  margin: var(--spacing-lg) auto;
  max-width: 400px;
}

.welcome-message h3 {
  margin: 0 0 10px 0;
  color: var(--vscode-textLink-foreground);
}

.welcome-message p {
  margin: 0;
  opacity: 0.8;
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 15px;
  color: var(--vscode-descriptionForeground);
  font-style: italic;
  font-size: 12px;
  margin-bottom: var(--spacing-sm);
}

.typing-dots {
  display: flex;
  gap: 3px;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--vscode-descriptionForeground);
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Time and Date Container */
#time-date-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--vscode-panel-border);
  background-color: var(--vscode-editor-background);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 3.5rem;
  box-sizing: border-box;
}

#time-date {
  font-size: var(--font-size-small);
  color: var(--vscode-descriptionForeground);
}

.status-icon {
  width: 20px;
  height: 20px;
}

.status-icon svg {
  width: 100%;
  height: 100%;
}

.connected-circle {
  fill: var(--vscode-charts-green);
}

.disconnected-circle {
  stroke: var(--vscode-charts-red);
}

/* Progress Bar */
#progress-bar-box {
  padding: var(--spacing-xs) var(--spacing-md);
  border-top: 1px solid var(--vscode-panel-border);
  background-color: var(--vscode-statusBar-background);
  color: var(--vscode-statusBar-foreground);
  font-size: var(--font-size-small);
}

#progress-bar-h {
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

#progress-bar-i {
  color: var(--vscode-charts-blue);
}

#progress-bar {
  flex: 1;
  color: var(--vscode-descriptionForeground);
}
