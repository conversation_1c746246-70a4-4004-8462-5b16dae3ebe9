/* Settings CSS for Agent Zero VS Code Extension */

/* Settings Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.modal-container {
  background-color: var(--vscode-editor-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--vscode-panel-border);
  background-color: var(--vscode-sideBar-background);
}

.modal-header h2 {
  margin: 0;
  color: var(--vscode-foreground);
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--vscode-foreground);
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background-color: var(--vscode-button-hoverBackground);
  border-radius: 4px;
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-md);
}

/* Settings Tabs */
.settings-tabs-container {
  margin-bottom: var(--spacing-lg);
}

.settings-tabs {
  display: flex;
  border-bottom: 1px solid var(--vscode-panel-border);
  overflow-x: auto;
}

.settings-tab {
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
  white-space: nowrap;
  font-size: var(--font-size-small);
}

.settings-tab.active {
  border-bottom-color: var(--vscode-textLink-foreground);
  color: var(--vscode-textLink-foreground);
}

.settings-tab:hover {
  background-color: var(--vscode-list-hoverBackground);
}

/* Settings Sections */
#settings-sections {
  display: flex;
  gap: var(--spacing-lg);
}

#settings-sections nav {
  flex: 0 0 200px;
}

#settings-sections nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

#settings-sections nav li {
  margin-bottom: var(--spacing-xs);
}

#settings-sections nav a {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  text-decoration: none;
  color: var(--vscode-foreground);
  border-radius: 4px;
  transition: background-color 0.2s;
}

#settings-sections nav a:hover {
  background-color: var(--vscode-list-hoverBackground);
}

#settings-sections nav img {
  width: 16px;
  height: 16px;
}

/* Field Styles */
.field {
  display: grid;
  grid-template-columns: 60% 1fr;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-xs) 0;
  gap: var(--spacing-md);
}

.field.field-full {
  grid-template-columns: 1fr;
}

.field-label {
  display: flex;
  flex-direction: column;
}

.field-title {
  font-weight: bold;
  color: var(--vscode-textLink-foreground);
  margin-bottom: var(--spacing-xxs);
}

.field-description {
  color: var(--vscode-descriptionForeground);
  font-size: var(--font-size-small);
  margin: 0;
}

.field-control {
  width: 100%;
  display: flex;
  align-items: center;
}

/* Input Styles */
input[type="text"],
input[type="password"],
input[type="number"],
textarea,
select {
  width: 100%;
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--vscode-input-border);
  border-radius: 4px;
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  font-family: var(--vscode-font-family);
  font-size: var(--vscode-font-size);
  outline: none;
  transition: border-color 0.2s;
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
  border-color: var(--vscode-focusBorder);
}

textarea {
  min-height: 100px;
  font-family: var(--vscode-editor-font-family);
  resize: vertical;
}

/* Range Input */
input[type="range"] {
  width: 100%;
  margin-right: var(--spacing-sm);
}

.range-value {
  min-width: 40px;
  text-align: center;
  font-size: var(--font-size-small);
  color: var(--vscode-descriptionForeground);
}

/* Toggle Switch */
.toggle {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggler {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--vscode-checkbox-background);
  transition: 0.4s;
  border-radius: 24px;
}

.toggler:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .toggler {
  background-color: var(--vscode-checkbox-selectBackground);
}

input:checked + .toggler:before {
  transform: translateX(26px);
}

/* Button Styles */
.btn {
  padding: var(--spacing-xs) var(--spacing-md);
  border: 1px solid var(--vscode-button-border);
  border-radius: 4px;
  cursor: pointer;
  font-size: var(--font-size-small);
  transition: background-color 0.2s;
}

.btn-field {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}

.btn-field:hover {
  background-color: var(--vscode-button-hoverBackground);
}

.btn-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Section Styles */
.section {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 8px;
  background-color: var(--vscode-editor-background);
}

.section-title {
  font-size: var(--font-size-large);
  font-weight: bold;
  color: var(--vscode-textLink-foreground);
  margin-bottom: var(--spacing-sm);
}

.section-description {
  color: var(--vscode-descriptionForeground);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-small);
}
