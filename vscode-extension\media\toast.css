/* Toast Notifications CSS for Agent Zero VS Code Extension */

/* Toast Container */
#toast {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10001;
  max-width: 400px;
  min-width: 300px;
  background-color: var(--vscode-notifications-background);
  border: 1px solid var(--vscode-notifications-border);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
}

#toast.show {
  transform: translateX(0);
}

/* Toast Content */
.toast__content {
  padding: var(--spacing-md);
}

.toast__title {
  font-weight: bold;
  color: var(--vscode-notifications-foreground);
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-normal);
}

.toast__separator {
  height: 1px;
  background-color: var(--vscode-notifications-border);
  margin: var(--spacing-xs) 0;
}

.toast__message {
  color: var(--vscode-notifications-foreground);
  font-size: var(--font-size-small);
  line-height: 1.4;
  word-wrap: break-word;
  max-height: 200px;
  overflow-y: auto;
}

/* Toast Buttons */
.toast__copy,
.toast__close {
  position: absolute;
  top: var(--spacing-xs);
  background: none;
  border: none;
  color: var(--vscode-notifications-foreground);
  cursor: pointer;
  padding: var(--spacing-xxs) var(--spacing-xs);
  border-radius: 4px;
  font-size: var(--font-size-small);
  transition: background-color 0.2s;
}

.toast__copy {
  right: 60px;
}

.toast__close {
  right: var(--spacing-xs);
}

.toast__copy:hover,
.toast__close:hover {
  background-color: var(--vscode-button-hoverBackground);
}

/* Toast Types */
.toast.success {
  border-left: 4px solid var(--vscode-charts-green);
}

.toast.success .toast__title {
  color: var(--vscode-charts-green);
}

.toast.error {
  border-left: 4px solid var(--vscode-charts-red);
}

.toast.error .toast__title {
  color: var(--vscode-charts-red);
}

.toast.warning {
  border-left: 4px solid var(--vscode-charts-yellow);
}

.toast.warning .toast__title {
  color: var(--vscode-charts-yellow);
}

.toast.info {
  border-left: 4px solid var(--vscode-charts-blue);
}

.toast.info .toast__title {
  color: var(--vscode-charts-blue);
}

/* Toast Progress Bar */
.toast__progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background-color: var(--vscode-textLink-foreground);
  transition: width 0.1s linear;
}

/* Toast Animation */
@keyframes toastSlideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes toastSlideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.toast.slide-in {
  animation: toastSlideIn 0.3s ease-out;
}

.toast.slide-out {
  animation: toastSlideOut 0.3s ease-in;
}

/* Multiple Toasts */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10001;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  max-height: calc(100vh - 40px);
  overflow-y: auto;
}

.toast-container .toast {
  position: relative;
  transform: none;
  margin-bottom: 0;
}

/* Toast with Icon */
.toast__icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: var(--spacing-xs);
  vertical-align: middle;
}

.toast__icon.success {
  color: var(--vscode-charts-green);
}

.toast__icon.error {
  color: var(--vscode-charts-red);
}

.toast__icon.warning {
  color: var(--vscode-charts-yellow);
}

.toast__icon.info {
  color: var(--vscode-charts-blue);
}

/* Toast Actions */
.toast__actions {
  display: flex;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
  justify-content: flex-end;
}

.toast__action {
  padding: var(--spacing-xxs) var(--spacing-sm);
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: 1px solid var(--vscode-button-border);
  border-radius: 4px;
  cursor: pointer;
  font-size: var(--font-size-small);
  transition: background-color 0.2s;
}

.toast__action:hover {
  background-color: var(--vscode-button-hoverBackground);
}

.toast__action.secondary {
  background-color: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
}

.toast__action.secondary:hover {
  background-color: var(--vscode-button-secondaryHoverBackground);
}

/* Responsive Design */
@media (max-width: 768px) {
  #toast,
  .toast-container {
    right: 10px;
    left: 10px;
    max-width: none;
    min-width: auto;
  }
  
  .toast__content {
    padding: var(--spacing-sm);
  }
  
  .toast__copy {
    right: 50px;
  }
  
  .toast__close {
    right: var(--spacing-xs);
  }
}

/* Dark/Light Mode Adjustments */
.light-mode #toast {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Accessibility */
.toast[role="alert"] {
  /* Ensure screen readers announce toast messages */
}

.toast__close:focus,
.toast__copy:focus,
.toast__action:focus {
  outline: 2px solid var(--vscode-focusBorder);
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  #toast {
    border-width: 2px;
  }
  
  .toast__copy,
  .toast__close {
    border: 1px solid var(--vscode-notifications-foreground);
  }
}
