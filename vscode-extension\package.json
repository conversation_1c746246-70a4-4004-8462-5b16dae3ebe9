{"name": "agent-zero-vscode", "displayName": "Agent Zero - AI Assistant", "description": "Autonomous AI Agent Framework integrated with Visual Studio Code", "version": "0.0.4", "publisher": "agent-zero-team", "engines": {"vscode": "^1.74.0"}, "categories": ["AI", "Machine Learning", "Other"], "keywords": ["ai", "agent", "automation", "assistant", "llm", "chatbot", "code-generation", "productivity"], "activationEvents": ["*"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "agent-zero.openChat", "title": "Open Agent Zero Chat", "category": "Agent <PERSON>", "icon": "$(robot)"}, {"command": "agent-zero.newAgent", "title": "Create New Agent", "category": "Agent <PERSON>", "icon": "$(add)"}, {"command": "agent-zero.showSettings", "title": "Agent Zero Settings", "category": "Agent <PERSON>", "icon": "$(settings-gear)"}, {"command": "agent-zero.executeCode", "title": "Execute Code with Agent Zero", "category": "Agent <PERSON>", "icon": "$(play)"}, {"command": "agent-zero.analyzeFile", "title": "Analyze File with Agent Zero", "category": "Agent <PERSON>", "icon": "$(search)"}, {"command": "agent-zero.generateCode", "title": "Generate Code", "category": "Agent <PERSON>", "icon": "$(code)"}, {"command": "agent-zero.searchKnowledge", "title": "Search Knowledge", "category": "Agent <PERSON>", "icon": "$(search)"}, {"command": "agent-zero.save<PERSON><PERSON><PERSON>", "title": "Save Memory", "category": "Agent <PERSON>", "icon": "$(save)"}, {"command": "agent-zero.createTask", "title": "Create New Task", "category": "Agent <PERSON>", "icon": "$(add)"}, {"command": "agent-zero.showTasks", "title": "Show Tasks", "category": "Agent <PERSON>", "icon": "$(list-unordered)"}, {"command": "agent-zero.openFileBrowser", "title": "Open File Browser", "category": "Agent <PERSON>", "icon": "$(folder)"}, {"command": "agent-zero.showChatHistory", "title": "Show Chat History", "category": "Agent <PERSON>", "icon": "$(history)"}, {"command": "agent-zero.exportChat", "title": "Export <PERSON>", "category": "Agent <PERSON>", "icon": "$(export)"}, {"command": "agent-zero.importChat", "title": "Import Chat", "category": "Agent <PERSON>", "icon": "$(import)"}, {"command": "agent-zero.clear<PERSON>hat", "title": "Clear Chat", "category": "Agent <PERSON>", "icon": "$(clear-all)"}, {"command": "agent-zero.toggleSidebar", "title": "Toggle Sidebar", "category": "Agent <PERSON>", "icon": "$(sidebar-expand)"}, {"command": "agent-zero.showAgentDashboard", "title": "Show Agent Dashboard", "category": "Agent <PERSON>", "icon": "$(dashboard)"}, {"command": "agent-zero.createAgentFromTemplate", "title": "Create Agent from Template", "category": "Agent <PERSON>", "icon": "$(add)"}, {"command": "agent-zero.createCustomAgent", "title": "Create Custom Agent", "category": "Agent <PERSON>", "icon": "$(gear)"}, {"command": "agent-zero.showProjectSummary", "title": "Show Project Summary", "category": "Agent <PERSON>", "icon": "$(info)"}, {"command": "agent-zero.askAboutProject", "title": "Ask About Project", "category": "Agent <PERSON>", "icon": "$(question)"}, {"command": "agent-zero.refreshProjectContext", "title": "Refresh Project Context", "category": "Agent <PERSON>", "icon": "$(refresh)"}], "menus": {"editor/context": [{"command": "agent-zero.analyzeFile", "when": "editorHasSelection", "group": "agent-zero"}, {"command": "agent-zero.generateCode", "group": "agent-zero"}], "explorer/context": [{"command": "agent-zero.analyzeFile", "when": "resourceExtname =~ /\\.(py|js|ts|java|cpp|c|cs|php|rb|go|rs|swift|kt)$/", "group": "agent-zero"}], "commandPalette": [{"command": "agent-zero.openChat"}, {"command": "agent-zero.newAgent"}, {"command": "agent-zero.showSettings"}, {"command": "agent-zero.executeCode"}, {"command": "agent-zero.analyzeFile"}, {"command": "agent-zero.generateCode"}]}, "viewsContainers": {"activitybar": [{"id": "agent-zero", "title": "Agent <PERSON>", "icon": "./media/agent-zero-icon.svg"}]}, "views": {"agent-zero": [{"id": "agent-zero-chat", "name": "Agent Zero WebUI"}, {"id": "agent-zero-advanced-chat", "name": "Advanced Chat"}, {"id": "agent-zero-agents", "name": "Active Agents"}, {"id": "agent-zero-memory", "name": "Memory & Knowledge"}, {"id": "agent-zero-tools", "name": "Tools"}, {"id": "agent-zero-enhanced-memory", "name": "Enhanced Memory"}, {"id": "agent-zero-multi-agents", "name": "Multi-Agent System"}]}, "configuration": {"title": "Agent <PERSON>", "properties": {"agent-zero.models.chat.provider": {"type": "string", "default": "openai", "enum": ["openai", "anthropic", "gemini", "groq", "ollama", "lm-studio", "mistral", "codestral", "deepseek", "azure", "openrouter", "sambanova", "huggingface", "other"], "description": "Chat model provider"}, "agent-zero.models.chat.name": {"type": "string", "default": "gpt-4", "description": "Chat model name"}, "agent-zero.models.utility.provider": {"type": "string", "default": "openai", "enum": ["openai", "anthropic", "gemini", "groq", "ollama", "lm-studio", "mistral", "codestral", "deepseek", "azure", "openrouter", "sambanova", "huggingface", "other"], "description": "Utility model provider"}, "agent-zero.models.utility.name": {"type": "string", "default": "gpt-3.5-turbo", "description": "Utility model name"}, "agent-zero.models.embeddings.provider": {"type": "string", "default": "openai", "enum": ["openai", "anthropic", "gemini", "groq", "ollama", "lm-studio", "mistral", "codestral", "deepseek", "azure", "openrouter", "sambanova", "huggingface", "sentence-transformers", "other"], "description": "Embeddings model provider"}, "agent-zero.models.embeddings.name": {"type": "string", "default": "text-embedding-ada-002", "description": "Embeddings model name"}, "agent-zero.agent.prompts": {"type": "string", "default": "agent0", "description": "Agent prompts subdirectory"}, "agent-zero.agent.memory": {"type": "string", "default": "default", "description": "Agent memory subdirectory"}, "agent-zero.agent.knowledge": {"type": "string", "default": "custom", "description": "Agent knowledge subdirectory"}, "agent-zero.execution.enableDocker": {"type": "boolean", "default": false, "description": "Enable Docker for code execution"}, "agent-zero.execution.enableSSH": {"type": "boolean", "default": false, "description": "Enable SSH for remote execution"}, "agentZero.autoScroll": {"type": "boolean", "default": true, "description": "Automatically scroll to new messages in chat"}, "agentZero.darkMode": {"type": "boolean", "default": true, "description": "Use dark mode theme for the interface"}, "agentZero.showThoughts": {"type": "boolean", "default": true, "description": "Show agent thinking process in chat"}, "agentZero.maxMessages": {"type": "number", "default": 100, "description": "Maximum number of messages to keep in chat history"}, "agentZero.enableFileAttachments": {"type": "boolean", "default": true, "description": "Enable file attachment functionality"}, "agentZero.enableTaskManagement": {"type": "boolean", "default": true, "description": "Enable task management features"}, "agentZero.enableMemorySystem": {"type": "boolean", "default": true, "description": "Enable persistent memory system"}, "agentZero.enableSearchFeatures": {"type": "boolean", "default": true, "description": "Enable search functionality across chat, files, and tasks"}, "agentZero.sidebarWidth": {"type": "number", "default": 250, "minimum": 200, "maximum": 400, "description": "Width of the sidebar panel in pixels"}, "agentZero.chatInputHeight": {"type": "number", "default": 40, "minimum": 30, "maximum": 120, "description": "Initial height of the chat input area in pixels"}, "agent-zero.ui.theme": {"type": "string", "default": "auto", "enum": ["auto", "light", "dark"], "description": "UI theme preference"}, "agent-zero.ui.language": {"type": "string", "default": "en", "enum": ["en", "ar"], "description": "UI language"}}}, "keybindings": [{"command": "agent-zero.openChat", "key": "ctrl+shift+a", "mac": "cmd+shift+a"}, {"command": "agent-zero.newAgent", "key": "ctrl+shift+n", "mac": "cmd+shift+n"}, {"command": "agent-zero.showSettings", "key": "ctrl+shift+comma", "mac": "cmd+shift+comma"}, {"command": "agent-zero.searchKnowledge", "key": "ctrl+shift+f", "mac": "cmd+shift+f"}, {"command": "agent-zero.save<PERSON><PERSON><PERSON>", "key": "ctrl+shift+m", "mac": "cmd+shift+m"}, {"command": "agent-zero.showProjectSummary", "key": "ctrl+shift+p", "mac": "cmd+shift+p"}, {"command": "agent-zero.executeCode", "key": "ctrl+shift+e", "mac": "cmd+shift+e", "when": "editorTextFocus"}]}, "scripts": {"vscode:prepublish": "npm run package", "compile": "webpack", "watch": "webpack --watch", "package": "webpack --mode production --devtool hidden-source-map", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "npm run compile-tests && npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "vsce-package": "vsce package", "publish": "vsce publish"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@types/ws": "^8.5.0", "@types/node-fetch": "^2.6.4", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "eslint": "^8.28.0", "typescript": "^4.9.4", "ts-loader": "^9.4.0", "webpack": "^5.76.0", "webpack-cli": "^5.0.0", "@vscode/test-electron": "^2.2.0", "@vscode/vsce": "^2.15.0"}, "dependencies": {"ws": "^8.13.0", "axios": "^1.4.0", "express": "^4.18.2", "socket.io": "^4.7.2", "socket.io-client": "^4.7.2", "node-fetch": "^2.6.7"}, "repository": {"type": "git", "url": "https://github.com/agent-zero-team/agent-zero-vscode.git"}, "bugs": {"url": "https://github.com/agent-zero-team/agent-zero-vscode/issues"}, "homepage": "https://github.com/agent-zero-team/agent-zero-vscode#readme", "license": "MIT"}