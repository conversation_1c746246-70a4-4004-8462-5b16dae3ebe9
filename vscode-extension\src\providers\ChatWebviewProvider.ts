import * as vscode from 'vscode';
import * as path from 'path';
import { AgentManager } from '../core/AgentManager';

export class ChatWebviewProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'agent-zero-chat';
    private _view?: vscode.WebviewView;
    private messages: any[] = [];
    private chats: any[] = [];
    private tasks: any[] = [];
    private currentContext: string = '';
    private connectionStatus: boolean = false;

    constructor(
        private readonly context: vscode.ExtensionContext,
        private readonly agentManager: AgentManager
    ) {}

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                this.context.extensionUri
            ]
        };

        webviewView.webview.html = this.getHtmlForWebview(webviewView.webview);

        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(
            async (data) => {
                switch (data.type || data.command) {
                    case 'sendMessage':
                        await this.handleSendMessage(data.message);
                        break;
                    case 'clearChat':
                        this.clearChat();
                        break;
                    case 'exportChat':
                        await this.exportChat();
                        break;
                    case 'importChat':
                        await this.importChat();
                        break;
                    case 'changeAgent':
                        this.changeActiveAgent(data.agentId);
                        break;
                    case 'showProjectSummary':
                        await this.handleShowProjectSummary();
                        break;
                    case 'askAboutProject':
                        await this.handleAskAboutProject(data.question);
                        break;
                }
            },
            undefined,
            this.context.subscriptions
        );
    }

    private async handleSendMessage(message: string): Promise<void> {
        if (!this._view) {
            return;
        }

        try {
            // Show user message immediately
            this._view.webview.postMessage({
                type: 'addMessage',
                message: {
                    id: Date.now().toString(),
                    content: message,
                    sender: 'user',
                    timestamp: new Date().toISOString()
                }
            });

            // Show typing indicator
            this._view.webview.postMessage({
                type: 'setTyping',
                isTyping: true
            });

            // Send message to agent
            const response = await this.agentManager.sendMessage(message);

            // Hide typing indicator
            this._view.webview.postMessage({
                type: 'setTyping',
                isTyping: false
            });

            // Show agent response
            this._view.webview.postMessage({
                type: 'addMessage',
                message: {
                    id: (Date.now() + 1).toString(),
                    content: response,
                    sender: 'agent',
                    timestamp: new Date().toISOString()
                }
            });

        } catch (error) {
            // Hide typing indicator
            this._view.webview.postMessage({
                type: 'setTyping',
                isTyping: false
            });

            // Show error message
            this._view.webview.postMessage({
                type: 'addMessage',
                message: {
                    id: (Date.now() + 1).toString(),
                    content: `Error: ${error}`,
                    sender: 'system',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }

    private clearChat(): void {
        if (this._view) {
            this._view.webview.postMessage({
                type: 'clearMessages'
            });
        }
    }

    private async exportChat(): Promise<void> {
        // Implementation for exporting chat history
        vscode.window.showInformationMessage('Chat export functionality coming soon!');
    }

    private async importChat(): Promise<void> {
        // Implementation for importing chat history
        vscode.window.showInformationMessage('Chat import functionality coming soon!');
    }

    private changeActiveAgent(agentId: string): void {
        if (this.agentManager.setActiveAgent(agentId)) {
            const agent = this.agentManager.getAgent(agentId);
            if (this._view && agent) {
                this._view.webview.postMessage({
                    type: 'agentChanged',
                    agent: {
                        id: agent.id,
                        name: agent.name,
                        type: agent.type,
                        status: agent.status
                    }
                });
            }
        }
    }

    private async handleShowProjectSummary(): Promise<void> {
        try {
            const summary = await this.agentManager.getProjectSummary();

            // Add project summary as a system message
            this.addMessage('system', 'Project Summary', summary);
            this.updateWebview();
        } catch (error) {
            this.addMessage('system', 'Error', `Failed to get project summary: ${error}`);
            this.updateWebview();
        }
    }

    private async handleAskAboutProject(question: string): Promise<void> {
        if (!question) return;

        try {
            // Add user question to chat
            this.addMessage('user', 'You', question);
            this.updateWebview();

            // Get answer from agent
            const answer = await this.agentManager.askAboutProject(question);

            // Add agent response to chat
            this.addMessage('assistant', 'Agent Zero', answer);
            this.updateWebview();
        } catch (error) {
            this.addMessage('system', 'Error', `Failed to get project information: ${error}`);
            this.updateWebview();
        }
    }

    public show(): void {
        if (this._view) {
            this._view.show?.(true);
        }
    }

    private getHtmlForWebview(webview: vscode.Webview): string {
        // Get resource URIs
        const scriptUri = this.getWebviewUri(webview, 'media', 'agent-zero-main.js');
        const styleUri = this.getWebviewUri(webview, 'media', 'main.css');
        const indexCssUri = this.getWebviewUri(webview, 'media', 'index.css');
        const messagesCssUri = this.getWebviewUri(webview, 'media', 'messages.css');
        const settingsCssUri = this.getWebviewUri(webview, 'media', 'settings.css');
        const modalsCssUri = this.getWebviewUri(webview, 'media', 'modals.css');
        const toastCssUri = this.getWebviewUri(webview, 'media', 'toast.css');
        const fileBrowserCssUri = this.getWebviewUri(webview, 'media', 'file_browser.css');

        // Use a nonce to only allow specific scripts to be run
        const nonce = getNonce();

        return this.getFullInterfaceHtml(webview, {
            scriptUri,
            styleUri,
            indexCssUri,
            messagesCssUri,
            settingsCssUri,
            modalsCssUri,
            toastCssUri,
            fileBrowserCssUri,
            nonce
        });
    }

    private getWebviewUri(webview: vscode.Webview, ...pathSegments: string[]): vscode.Uri {
        const diskPath = vscode.Uri.joinPath(this.context.extensionUri, ...pathSegments);
        return webview.asWebviewUri(diskPath);
    }

    private getFullInterfaceHtml(webview: vscode.Webview, uris: any): string {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
            <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${uris.nonce}' 'unsafe-eval'; img-src ${webview.cspSource} data:; font-src ${webview.cspSource};">
            <title>Agent Zero</title>

            <!-- CSS Files -->
            <link rel="stylesheet" href="${uris.indexCssUri}">
            <link rel="stylesheet" href="${uris.messagesCssUri}">
            <link rel="stylesheet" href="${uris.settingsCssUri}">
            <link rel="stylesheet" href="${uris.modalsCssUri}">
            <link rel="stylesheet" href="${uris.toastCssUri}">
            <link rel="stylesheet" href="${uris.fileBrowserCssUri}">

            <!-- Font Awesome for icons -->
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
        </head>
        <body class="dark-mode">
            <div class="container">
                <div id="sidebar-overlay" class="sidebar-overlay hidden"></div>

                <!-- Sidebar Toggle and Logo -->
                <div class="icons-section" id="hide-button">
                    <button id="toggle-sidebar" class="toggle-sidebar-button" aria-label="Toggle Sidebar">
                        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M3 13h18v-2H3v2zm0 4h18v-2H3v2zm0-8h18V7H3v2z"></path>
                        </svg>
                    </button>
                    <div id="logo-container">
                        <img src="${this.getWebviewUri(webview, 'media', 'icons', 'agent.svg')}" alt="Agent Zero" width="22" height="22">
                    </div>
                </div>

                <!-- Left Panel (Sidebar) -->
                <div id="left-panel" class="panel">
                    <div class="left-panel-top">
                        <!-- Control Buttons -->
                        <div class="config-section">
                            <button class="config-button" id="resetChat">Reset Chat</button>
                            <button class="config-button" id="newChat">New Chat</button>
                            <button class="config-button" id="loadChats">Load Chat</button>
                            <button class="config-button" id="saveChat">Save Chat</button>
                            <button class="config-button" id="restart">Restart</button>
                            <button class="config-button" id="settings">Settings</button>
                        </div>

                        <!-- Tabs -->
                        <div class="tabs-container">
                            <div class="tabs">
                                <div class="tab active" id="chats-tab">Chats</div>
                                <div class="tab" id="tasks-tab">Tasks</div>
                            </div>
                        </div>

                        <!-- Chats List -->
                        <div class="config-section" id="chats-section">
                            <div class="chats-list-container">
                                <ul class="config-list" id="chats-list">
                                    <!-- Chats will be populated here -->
                                </ul>
                                <div class="empty-list-message" id="empty-chats-message" style="display: none;">
                                    <p><i>No chats to list.</i></p>
                                </div>
                            </div>
                        </div>

                        <!-- Tasks List -->
                        <div class="config-section" id="tasks-section" style="display: none;">
                            <div class="tasks-list-container">
                                <ul class="config-list" id="tasks-list">
                                    <!-- Tasks will be populated here -->
                                </ul>
                                <div class="empty-list-message" id="empty-tasks-message" style="display: none;">
                                    <p><i>No tasks to list.</i></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Preferences Section -->
                    <div class="left-panel-bottom">
                        <div class="pref-section">
                            <h3 class="pref-header">Preferences</h3>
                            <ul class="config-list" id="pref-list">
                                <li>
                                    <span>Autoscroll</span>
                                    <label class="switch">
                                        <input id="auto-scroll-switch" type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                </li>
                                <li>
                                    <span>Dark mode</span>
                                    <label class="switch">
                                        <input id="dark-mode-switch" type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                </li>
                                <li>
                                    <span>Show thoughts</span>
                                    <label class="switch">
                                        <input id="show-thoughts-switch" type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                </li>
                            </ul>
                        </div>

                        <!-- Version Info -->
                        <div class="version-info">
                            <span id="a0version">Agent Zero VS Code Extension</span>
                        </div>
                    </div>
                </div>

                <!-- Right Panel (Chat Area) -->
                <div id="right-panel" class="panel">
                    <!-- Time and Status -->
                    <div id="time-date-container">
                        <div id="time-date"></div>
                        <div class="status-icon">
                            <svg viewBox="0 0 30 30">
                                <circle class="connected-circle" cx="15" cy="15" r="8" fill="#00c340" opacity="1" />
                            </svg>
                        </div>
                    </div>

                    <!-- Chat History -->
                    <div id="chat-history">
                        <div class="welcome-message">
                            <h3>Welcome to Agent Zero!</h3>
                            <p>I'm your AI assistant. How can I help you today?</p>
                        </div>
                    </div>

                    <!-- Toast Notifications -->
                    <div id="toast" class="toast" style="display: none;">
                        <div class="toast__content">
                            <div class="toast__title"></div>
                            <div class="toast__separator"></div>
                            <div class="toast__message"></div>
                        </div>
                        <button class="toast__copy" style="display: none;">Copy</button>
                        <button class="toast__close">Close</button>
                    </div>

                    <!-- Progress Bar -->
                    <div id="progress-bar-box">
                        <h4 id="progress-bar-h">
                            <span id="progress-bar-i">|></span><span id="progress-bar"></span>
                        </h4>
                    </div>

                    <!-- Input Section -->
                    <div id="input-section">
                        <!-- File Preview Section -->
                        <div class="preview-section" id="preview-section" style="display: none;">
                            <!-- File previews will be added here -->
                        </div>

                        <!-- Input Row -->
                        <div class="input-row">
                            <!-- Attachment Button -->
                            <div class="attachment-wrapper">
                                <label for="file-input" class="attachment-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M16.5 6v11.5c0 2.21-1.79 4-4 4s-4-1.79-4-4V5c0-1.38 1.12-2.5 2.5-2.5s2.5 1.12 2.5 2.5v10.5c0 .55-.45 1-1 1s-1-.45-1-1V6H10v9.5c0 1.38 1.12 2.5 2.5 2.5s2.5-1.12 2.5-2.5V5c0-2.21-1.79-4-4-4S7 2.79 7 5v12.5c0 3.04 2.46 5.5 5.5 5.5s5.5-2.46 5.5-5.5V6h-1.5z" />
                                    </svg>
                                </label>
                                <input type="file" id="file-input" accept="*" multiple style="display: none">
                            </div>

                            <!-- Chat Input Container -->
                            <div id="chat-input-container">
                                <textarea id="chat-input" placeholder="Type your message here..." rows="1"></textarea>
                                <button id="expand-button" aria-label="Expand input">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
                                    </svg>
                                </button>
                            </div>

                            <!-- Chat Buttons -->
                            <div id="chat-buttons-wrapper">
                                <button class="chat-button" id="send-button" aria-label="Send message">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
                                        <path d="M25 20 L75 50 L25 80" fill="none" stroke="currentColor" stroke-width="15"></path>
                                    </svg>
                                </button>
                                <button class="chat-button mic-inactive" id="microphone-button" aria-label="Start/Stop recording">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 18" fill="currentColor">
                                        <path d="m8,12c1.66,0,3-1.34,3-3V3c0-1.66-1.34-3-3-3s-3,1.34-3,3v6c0,1.66,1.34,3,3,3Zm-1,1.9c-2.7-.4-4.8-2.6-5-5.4H0c.2,3.8,3.1,6.9,7,7.5v2h2v-2c3.9-.6,6.8-3.7,7-7.5h-2c-.2,2.8-2.3,5-5,5.4h-2Z" />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Text Buttons Row -->
                        <div class="text-buttons-row">
                            <button class="text-button" id="pause-agent">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" width="14" height="14">
                                    <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"></path>
                                </svg>
                                <span>Pause Agent</span>
                            </button>
                            <button class="text-button" id="load-knowledge">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5"></path>
                                </svg>
                                <p>Import knowledge</p>
                            </button>
                            <button class="text-button" id="work_dir_browser">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 123.37 92.59">
                                    <path d="m5.72,11.5l-3.93,8.73h119.77s-3.96-8.73-3.96-8.73h-60.03c-1.59,0-2.88-1.29-2.88-2.88V1.75H13.72v6.87c0,1.59-1.29,2.88-2.88,2.88h-5.12Z" fill="none" stroke="currentColor" stroke-linejoin="round" stroke-width="7"></path>
                                    <path d="m6.38,20.23H1.75l7.03,67.03c.11,1.07.55,2.02,1.2,2.69.55.55,1.28.89,2.11.89h97.1c.82,0,1.51-.33,2.05-.87.68-.68,1.13-1.67,1.28-2.79l9.1-66.94H6.38Z" fill="none" stroke="currentColor" stroke-linejoin="round" stroke-width="8"></path>
                                </svg>
                                <p>Files</p>
                            </button>
                            <button class="text-button" id="history_inspect">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="5 10 85 85">
                                    <path fill="currentColor" d="m59.572,57.949c-.41,0-.826-.105-1.207-.325l-9.574-5.528c-.749-.432-1.21-1.231-1.21-2.095v-14.923c0-1.336,1.083-2.419,2.419-2.419s2.419,1.083,2.419,2.419v13.526l8.364,4.829c1.157.668,1.554,2.148.886,3.305-.448.776-1.261,1.21-2.097,1.21Z"></path>
                                </svg>
                                <p>History</p>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Scripts -->
            <script nonce="${uris.nonce}" src="${this.getWebviewUri(webview, 'media', 'agent-zero-main.js')}"></script>
        </body>
        </html>`;
    }

    private addMessage(sender: string, name: string, content: string): void {
        const message = {
            id: Date.now().toString(),
            sender,
            name,
            content,
            timestamp: new Date()
        };

        this.messages.push(message);

        if (this._view) {
            this._view.webview.postMessage({
                type: 'addMessage',
                message: message
            });
        }
    }

    private updateWebview(): void {
        if (this._view) {
            this._view.webview.postMessage({
                type: 'loadMessages',
                messages: this.messages
            });
        }
    }
}

function getNonce() {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
        text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
}
