import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';

export class OriginalWebUIProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'agent-zero-original-webui';
    private _view?: vscode.WebviewView;

    constructor(private readonly context: vscode.ExtensionContext) {}

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                vscode.Uri.file(path.join(this.context.extensionPath, '..', 'webui')),
                this.context.extensionUri
            ]
        };

        webviewView.webview.html = this.getOriginalWebUI(webviewView.webview);
    }

    public show(): void {
        if (this._view) {
            this._view.show?.(true);
        }
    }

    private getOriginalWebUI(webview: vscode.Webview): string {
        try {
            // Path to the original webui
            const webuiPath = path.join(this.context.extensionPath, '..', 'webui');
            const indexPath = path.join(webuiPath, 'index.html');
            
            // Check if webui exists
            if (!fs.existsSync(indexPath)) {
                return this.getErrorHtml('Original webui not found. Please ensure the webui folder is in the correct location.');
            }

            // Read the original index.html
            let html = fs.readFileSync(indexPath, 'utf8');

            // Convert relative paths to webview URIs
            html = this.convertPathsToWebviewUris(html, webview, webuiPath);

            return html;
        } catch (error) {
            console.error('Error loading original webui:', error);
            return this.getErrorHtml(`Error loading webui: ${error}`);
        }
    }

    private convertPathsToWebviewUris(html: string, webview: vscode.Webview, webuiPath: string): string {
        // Convert CSS links
        html = html.replace(/href="([^"]+\.css)"/g, (match, cssPath) => {
            if (!cssPath.startsWith('http')) {
                const fullPath = path.join(webuiPath, cssPath);
                if (fs.existsSync(fullPath)) {
                    const uri = webview.asWebviewUri(vscode.Uri.file(fullPath));
                    return `href="${uri}"`;
                }
            }
            return match;
        });

        // Convert JS script sources
        html = html.replace(/src="([^"]+\.js)"/g, (match, jsPath) => {
            if (!jsPath.startsWith('http')) {
                const fullPath = path.join(webuiPath, jsPath);
                if (fs.existsSync(fullPath)) {
                    const uri = webview.asWebviewUri(vscode.Uri.file(fullPath));
                    return `src="${uri}"`;
                }
            }
            return match;
        });

        // Convert image sources and favicons
        html = html.replace(/(src|href)="([^"]+\.(png|jpg|jpeg|gif|svg|ico))"/g, (match, attr, imgPath) => {
            if (!imgPath.startsWith('http')) {
                const fullPath = path.join(webuiPath, imgPath);
                if (fs.existsSync(fullPath)) {
                    const uri = webview.asWebviewUri(vscode.Uri.file(fullPath));
                    return `${attr}="${uri}"`;
                }
            }
            return match;
        });

        // Convert vendor files (flatpickr, alpine, etc.)
        html = html.replace(/(src|href)="(vendor\/[^"]+)"/g, (match, attr, vendorPath) => {
            const fullPath = path.join(webuiPath, vendorPath);
            if (fs.existsSync(fullPath)) {
                const uri = webview.asWebviewUri(vscode.Uri.file(fullPath));
                return `${attr}="${uri}"`;
            }
            return match;
        });

        // Convert public folder assets
        html = html.replace(/(src|href)="(public\/[^"]+)"/g, (match, attr, publicPath) => {
            const fullPath = path.join(webuiPath, publicPath);
            if (fs.existsSync(fullPath)) {
                const uri = webview.asWebviewUri(vscode.Uri.file(fullPath));
                return `${attr}="${uri}"`;
            }
            return match;
        });

        // Convert components folder
        html = html.replace(/(src|href)="(components\/[^"]+)"/g, (match, attr, componentPath) => {
            const fullPath = path.join(webuiPath, componentPath);
            if (fs.existsSync(fullPath)) {
                const uri = webview.asWebviewUri(vscode.Uri.file(fullPath));
                return `${attr}="${uri}"`;
            }
            return match;
        });

        // Convert js folder files
        html = html.replace(/(src|href)="(js\/[^"]+)"/g, (match, attr, jsPath) => {
            const fullPath = path.join(webuiPath, jsPath);
            if (fs.existsSync(fullPath)) {
                const uri = webview.asWebviewUri(vscode.Uri.file(fullPath));
                return `${attr}="${uri}"`;
            }
            return match;
        });

        return html;
    }

    private getErrorHtml(errorMessage: string): string {
        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Agent Zero WebUI Error</title>
                <style>
                    body {
                        font-family: var(--vscode-font-family);
                        background-color: var(--vscode-editor-background);
                        color: var(--vscode-foreground);
                        padding: 20px;
                        margin: 0;
                    }
                    .error-container {
                        text-align: center;
                        padding: 40px 20px;
                    }
                    .error-icon {
                        font-size: 48px;
                        margin-bottom: 20px;
                    }
                    .error-title {
                        font-size: 24px;
                        font-weight: bold;
                        margin-bottom: 10px;
                        color: var(--vscode-errorForeground);
                    }
                    .error-message {
                        font-size: 16px;
                        line-height: 1.5;
                        margin-bottom: 20px;
                        opacity: 0.8;
                    }
                    .error-help {
                        font-size: 14px;
                        opacity: 0.6;
                        border-top: 1px solid var(--vscode-panel-border);
                        padding-top: 20px;
                        margin-top: 20px;
                    }
                </style>
            </head>
            <body>
                <div class="error-container">
                    <div class="error-icon">⚠️</div>
                    <div class="error-title">WebUI Loading Error</div>
                    <div class="error-message">${errorMessage}</div>
                    <div class="error-help">
                        <p>Make sure the 'webui' folder is located in the same directory as the 'vscode-extension' folder.</p>
                        <p>Expected structure:</p>
                        <pre>
agent-zero/
├── webui/
│   ├── index.html
│   ├── index.js
│   └── ...
└── vscode-extension/
    └── ...
                        </pre>
                    </div>
                </div>
            </body>
            </html>
        `;
    }
}
